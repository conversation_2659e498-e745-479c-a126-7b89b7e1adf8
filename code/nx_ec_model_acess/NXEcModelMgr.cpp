/**********************************************************************
* NXEcModelMgr.cpp         author:jjl      date:09/09/2013            
*---------------------------------------------------------------------
*  note:对外通信模型管理类实现文件定义                                                               
*  
**********************************************************************/

#include "NXEcModelMgr.h"


/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
CNXEcModelMgr::~CNXEcModelMgr()
{

}

/**
* @brief         构造函数
* @param[in]     CLogRecord * pLogRecord:日志对象指针
* @param[in]     bool bLoadAllStation: 第一次启动时是否自动加载所有变电站模型(默认为是,否则由调用者通过其它接口加载)
* @param[in]     int nReserve: 备用
* @param[out]    无
* @return        无
*/
CNXEcModelMgr::CNXEcModelMgr(CLogRecord * pLogRecord,bool bLoadAllStation,int nReserve)
	:CNXECObject(NULL,"CNXEcModelMgr"),m_bLoadAllStationModel(bLoadAllStation)
{
	m_bSelfLog  = false;
	m_bModelOk  = false;
	m_pDbmModel = NULL;
	__ClearAllListAndMap();

	// 日志对象可以由主进程第一次创建该实例时传入,日志即可与主进程写在同一文件中
	// 如果不使用主进程传入的日志对象初始化CNXECObject,则自己创建单独日志
	if( NULL == m_pLogRecord ) // 创建独立日志
	{
		if( NULL != pLogRecord ) 
			_SetLogPrint2Screen(pLogRecord->GetLogPrint2Screen());// 获取是否打印到屏幕标识
		// 模块名
		_SetLogModuleName("model_access");
		_OpenLogRecord();
		m_bSelfLog = true;
	}

	SetRunTimeObjName(SHARE_LIB_MODEL_ACCESS);

	try{
		// 初始化模型
		m_bModelOk = _Init();
	}
	catch (...)
	{
		RecordErrorLog("_Init()发生异常");
	}

	// 记录日志
	if( !m_bModelOk )
	{
		RecordErrorLog("_Init()失败,模型读取及建立失败");
	}
	else
	{
		RecordTraceLog("_Init()成功，模型读取及建立完毕");
	}
}

/**
* @brief         获取主控类单实例指针
* @param[in]     CLogRecord * pLogRecord:日志对象指针（仅实例第一次创建时有效)
* @param[in]     bool bLoadAllStation: 第一次启动时是否自动加载所有变电站模型(默认为是,否则由调用者通过其它接口加载)
* @param[in]     int nReserve: 备用
* @param[out]    无
* @return        CNXMainController * :对象指针
*/
CNXEcModelMgr * CNXEcModelMgr::GetNXEcModelMgrIns( CLogRecord * pLogRecord,bool bLoadAllStation,int nReserve)
{
	if ( NULL == CNXEcModelMgr::sm_pModelMgrInstance )
	{
		CNXEcModelMgr::sm_pModelMgrInstance = new CNXEcModelMgr( pLogRecord,bLoadAllStation,nReserve);

		if ( NULL == CNXEcModelMgr::sm_pModelMgrInstance )
		{
			printf("new CNXEcModelMgr()失败\n");
			return NULL;
		}

		// 判断内部模型是否成功初始化
		if(!CNXEcModelMgr::sm_pModelMgrInstance->GetModeState())
		{
			CNXEcModelMgr::sm_pModelMgrInstance->_FreeResource();
			delete CNXEcModelMgr::sm_pModelMgrInstance;
			CNXEcModelMgr::sm_pModelMgrInstance = NULL;
			return NULL;
		}
	}

	return CNXEcModelMgr::sm_pModelMgrInstance;
}


/**
* @brief         重新读取及初始化系统全部模型
* @param[in]     无
* @param[out]    无
* @return        true :true-成功 false-失败
*/
bool CNXEcModelMgr::RefreshAllModel()
{
	// 释放资源
	_FreeResource();

	// 关闭日志
	_CloseLogRecord();

	// 重新读取所有模型
	m_bModelOk = _Init();

	return m_bModelOk;
}

/**
* @brief         获取模型状态
* @param[in]     无
* @param[out]    无
* @return        true :true-模型成功建立 false-模型建立失败
*/
bool CNXEcModelMgr::GetModeState()
{
	return m_bModelOk;
}

/**
* @brief         获得系统基本配置
* @param[in]     bool bRefresh:是否重新从数据库读取列表 true:重新从数据库读取 false:直接返回已有信息
* @param[out]    BASIC_CFG_TB& basicCfg:保存系统基本配置信息;
* @return        bool true-成功 false-失败
*/
bool CNXEcModelMgr::GetBasicCfg(OUT BASIC_CFG_TB& basicCfg,IN bool bRefresh /*= false*/)
{
	char cError[255] = "";

	// 无需重新读取
	if( !bRefresh )
	{
		basicCfg = m_BasicCfg;
		return true;
	}

	// 重新读取配置
	if( !__ReadBaseCfg())
	{
		RecordErrorLog("GetBasicCfg()重新读取系统基本配置失败");
		return false;
	}

	basicCfg = m_BasicCfg;

	return true;
}

/**
* @brief         获得指定功能的超时时间(单位：秒)
* @param[in]     UINT nFun:指定的功能编号(功能号参见inxmb.def.h定义)
* @param[in]     bool bRefresh:是否重新从数据库读取列表 true:重新从数据库读取 false:直接返回已有信息
* @return        int:超时时间(秒)
*/
int CNXEcModelMgr::GetFunTimeOut( IN UINT nFun,IN bool bRefresh /*= false*/)
{
	char cError[255]    = "";
	CAutoLockOnStack tempLock(&m_LockForTimeOut);
	EC_FUN2TIMEOUT_MAP::iterator ite;

	if( !bRefresh )
	{
		ite = m_TimeOutMap.find(nFun);
		if( ite != m_TimeOutMap.end() )
		{
			//sprintf(cError,"GetFunTimeOut()获取FUN_ID=%d的超时为%d秒",nFun,ite->second->n_tmout_value);
			//RecordTraceLog(cError);
			return ite->second->n_tmout_value;
		}
		else
		{
		//	sprintf(cError,"GetFunTimeOut()FUN_ID=%d的超时没有配置,默认15秒",nFun);
		//	RecordErrorLog(cError);
			return 15;//(默认超时15秒)
		}
	}

	// 重新读取
	if( !__ReadTimeOutCfg() )
	{
		RecordErrorLog("GetFunTimeOut()重新读取超时配置失败");
		return 15;
	}

	ite = m_TimeOutMap.find(nFun);
	if( ite != m_TimeOutMap.end() )
	{
		sprintf(cError,"GetFunTimeOut()获取FUN_ID=%d的超时为%d秒",nFun,ite->second->n_tmout_value);
		RecordTraceLog(cError);
		return ite->second->n_tmout_value;
	}
	else
	{
		sprintf(cError,"GetFunTimeOut()FUN_ID=%d的超时没有配置,默认15秒",nFun);
		RecordErrorLog(cError);
		return 15;//(默认超时15秒)
	}
}

/**
* @brief         获得指定通信状态变化编号的原因描述
* @param[in]     UINT nReasonID:指定的原因编号(原因号参见数据库表定义)
* @param[out]    OUT string &strReason:原因描述
* @param[in]     bool bRefresh:是否重新从数据库读取列表 true:重新从数据库读取 false:直接返回已有信息
* @return        bool true-成功 false-失败
*/
bool CNXEcModelMgr::GetComuStatusReason( IN UINT nResonID,OUT string &strReason,IN bool bRefresh /*= false*/ )
{
	char cError[255]    = "";
	CAutoLockOnStack tempLock(&m_LockForReason);
	EC_ID2CHGREASON_MAP::iterator ite;

	if( !bRefresh )
	{
		ite = m_ComuStatusReasonMap.find(nResonID);
		if( ite != m_ComuStatusReasonMap.end() )
		{
			sprintf(cError,"获取REASON_ID=%d的原因:%s",nResonID,ite->second->str_aliasname.c_str());
			RecordTraceLog(cError);
			strReason = ite->second->str_aliasname;
			return true;
		}
		else
		{
			sprintf(cError,"没有找到REASON_ID=%d的原因配置",nResonID);
			RecordErrorLog(cError);
			return false;
		}
	}

	// 重新读取
	if( !__ReadComuStatusChgReasonCfg() )
	{
		RecordErrorLog("GetComuStatusReason()重新读取通信状态变化原因配置失败");
		return false;
	}

	ite = m_ComuStatusReasonMap.find(nResonID);
	if( ite != m_ComuStatusReasonMap.end() )
	{
		sprintf(cError,"GetComuStatusReason()获取REASON_ID=%d的原因:%s",nResonID,ite->second->str_aliasname.c_str());
		RecordTraceLog(cError);
		strReason = ite->second->str_aliasname;
		return true;
	}
	else
	{
		sprintf(cError,"GetComuStatusReason()没有找到REASON_ID=%d的原因配置",nResonID);
		RecordErrorLog(cError);
		return false;
	}
	return true;
}

/**
* @brief         获取对外通信所有客户端配置链表指针
* @brief[in]     bool bRefresh:是否重新从数据库读取列表 true:重新从数据库读取 false:直接返回内存信息
* @brief[out]    LIST_CLIENT & : 对外通信客户端配置链表
* @return        bool true-成功 false-失败
*/
bool CNXEcModelMgr::GetWholeEcClientList(OUT LIST_CLIENT & ClientList,IN bool bRefresh /*= false*/)
{
	char cError[255] = "";
	LIST_CLIENT::iterator ite ;
	CAutoLockOnStack tempLock(&m_LockForClient);

	if( &ClientList == &m_ClientList)
		return true;

	// 无需重新读取且有数据
	if( ( !bRefresh ) && (m_ClientList.size() > 0 ) )
	{
		// 复制各元素
		ClientList.assign(m_ClientList.begin(),m_ClientList.end());
		return true;
	}

	// 清空client list
	__FreeClientCfg();

	// 重新读取规约链表
	if( !__ReadProtocolCfg() )
	{
		RecordErrorLog("GetWholeEcClientList()重新读取规约配置失败");
		return false;
	}

	// 重新读取客户端列表
	if( !__ReadClientCfg() )
	{
		RecordErrorLog("GetWholeEcClientList()重新读取客户端配置失败");
		return false;
	}

	ClientList.assign(m_ClientList.begin(),m_ClientList.end());
	return true;
}

/**
* @brief         获取对外通信监听端口配置
* @brief[in]     bool bRefresh:是否重新从数据库读取列表 true:重新从数据库读取 false:直接返回内存信息
* @brief[out]    LIST_LISTEN & : 对外通信监听端口配置链表
* @return        bool true-成功 false-失败
*/
bool CNXEcModelMgr::GetEcListenList( OUT LIST_LISTEN & ltnList,IN bool bRefresh/* = false*/)
{
	char cError[255] = "";

	CAutoLockOnStack tempLock(&m_LockForListen);

	if( &ltnList == &m_ListenList)
		return true;

	// 无需重新读取且有数据
	if( ( !bRefresh ) && (m_ListenList.size() > 0 ) )
	{
		// 清除原先元素并附新元素
		ltnList.assign(m_ListenList.begin(),m_ListenList.end());
		return true;
	}

	// 重新读取监听列表
	if( !__ReadListenCfg() )
	{
		RecordErrorLog("GetEcListenList()重新读取监听端口配置失败");
		return false;
	}

	ltnList.assign(m_ListenList.begin(),m_ListenList.end());
	return true;
}

/**
* @brief         获取变电站模型指针
* @param[in]     int nStationID:变电站编号(默认-1，表示取首站配置,一般仅一个变电站时(子站)使用)
* @param[out]    无
* @return        EC_SUBSTATION *;变电站配置指针
*/
EC_SUBSTATION*  CNXEcModelMgr::GetSubStationModel( int nStationID /*= -1*/)
{
	char cError[255] = "";
	EC_SUBSTATION_MAP::iterator ite;
	CAutoLockOnStack tempLock(&m_LockForSubStation);

	if( m_SubStationMap.empty())
	{
		sprintf(cError,"GetSubStationModel()获取模型时，没有找到StationId=%d的模型",nStationID);
		RecordErrorLog(cError);
		return NULL;
	}

	if( -1 == nStationID )
	{
		// 返回首元素
		ite = m_SubStationMap.begin();
		return ite->second;
	}

	ite= m_SubStationMap.find(nStationID);
	if( ite != m_SubStationMap.end() )
	{
		return ite->second;
	}

	sprintf(cError,"GetSubStationModel()获取模型时，没有找到StationId=%d的模型",nStationID);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取变电站基本信息链表(不包括任何子模型)
* @brief[in]     bool bRefresh:是否重新从数据库读取列表 true:重新从数据库读取 false:直接返回内存信息
* @param[out]    LIST_SUBSTATION &StationList:保存变电站列表基本信息
* @return        bool :true-成功 false-失败 
*/
bool CNXEcModelMgr::GetSubStationList(OUT LIST_SUBSTATION &StationList,IN bool bRefresh /*=false*/)
{
	char cError[255] = "";

	CAutoLockOnStack tempLock(&m_LockForSubStation);

	if( &StationList == &m_SubStationList)
		return true;

	// 无需重新读取且有数据
	if( ( !bRefresh ) && (m_SubStationList.size() > 0 ) )
	{
		// 清除原先元素并附则新元素
		StationList.assign(m_SubStationList.begin(),m_SubStationList.end());
		return true;
	}

	// 重新读取变电站列表
	if( NULL == m_pDbmModel )
		return false;

	// 读取配置
	m_pDbmModel->nxmodel_get_substation(StationList);
	int nListSize = StationList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"nxmodel_get_substation()返回结果中记录数为%d,读取变电站配置失败",nListSize);
		RecordErrorLog(cError);
		return false; 
	}
	sprintf(cError,"读取变电站记录数为%d条",nListSize);
	RecordTraceLog(cError);

	// 重新更新变电站链表
	m_SubStationList.assign(StationList.begin(),StationList.end());
	return true;
}

/**
* @brief         完成连接数据库、读取数据并组建模型等初始化任务
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_Init()
{
	// 初始化DBM
	if( !__InitDbm())
		return false;

	// 锁定各对象
	CAutoLockOnStack stationLock(&m_LockForSubStation);
	CAutoLockOnStack proLock(&m_LockForProtocol);
	CAutoLockOnStack comLock(&m_LockForReason);
	CAutoLockOnStack tmoutLock(&m_LockForTimeOut);
	CAutoLockOnStack cliLock(&m_LockForClient);
	CAutoLockOnStack lisLock(&m_LockForListen);
	CAutoLockOnStack listIed(&m_LockForIed);

	// 读取系统基本配置
	if( !__ReadBaseCfg())
		return false;

	// 重新打开日志
	_SetLogRecordDays(m_BasicCfg.n_rcd_days);
	_SetLogLevel(m_BasicCfg.n_log_level);
	_SetLogRootPath(m_BasicCfg.str_logroot_path.c_str());
	_OpenLogRecord();

	// 读取超时配置
	if( !__ReadTimeOutCfg())
		return false;

	// 读取规约配置
	if( !__ReadProtocolCfg())
		return false;

	// 读取通信状态原因配置
	if( !__ReadComuStatusChgReasonCfg())
		return false;

	// 读取客户端配置
	if( !__ReadClientCfg())
		return false;

	// 读取监听配置
	if( !__ReadListenCfg())
		return false;

	// 读取不带LD的IED配置
	if( !__ReadAllIedBasicCfg(m_IedList,false))
		return false;

	if( m_bLoadAllStationModel )
	{
		// 读取所有变电站的全部模型
		if( !__ReadAllSubStationWholeCfg())
			return false;
	}

	RecordTraceLog("初始化全部模型配置成功");

	return true;
}

/**
* @brief         释放资源
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_FreeResource()
{
	// 锁定各对象
	CAutoLockOnStack stationLock(&m_LockForSubStation);
	CAutoLockOnStack proLock(&m_LockForProtocol);
	CAutoLockOnStack comLock(&m_LockForReason);
	CAutoLockOnStack tmoutLock(&m_LockForTimeOut);
	CAutoLockOnStack cliLock(&m_LockForClient);
	CAutoLockOnStack lisLock(&m_LockForListen);
	CAutoLockOnStack listIed(&m_LockForIed);

	__FreeAllSubStationCfg();

	__FreeClientCfg();

	__ClearAllListAndMap();

	__FreeDbm();

	if( m_bSelfLog )
		_CloseLogRecord();

	m_bModelOk = false;

	return true;
}

/**
* @brief         加装基础模型访问库、连接数据库; 打开日志文件
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__InitDbm()
{
	char cError[255]= "";
	// 加载基础模型库
	if( !m_NxLoadDbmLib.load_nxdbm_lib())
	{
		RecordErrorLog("load_nxdbm_lib()返回false,加载基础模型库失败");
		return false;
	}

	// 创建基础模型管理对象;
	m_pDbmModel = m_NxLoadDbmLib.create_nxdbm_inst();
	if( NULL == m_pDbmModel)
	{
		RecordErrorLog("create_nxdbm_inst()返回为NULL,基础模型库无法使用");
		return false;
	}

	// 初始化基础模型库
	try
	{
		if( m_pDbmModel->nxmodel_init(m_pLogRecord) != 0 )
		{
			RecordErrorLog("nxmodel_init()失败,基础模型库无法使用");
			return false;
		}
	}
	catch (...)
	{
		sprintf(cError,"nxmodel_init()异常,原因:%s(%d)",strerror(errno),errno);
		RecordErrorLog(cError);
		return false;
	}

	RecordTraceLog("基础模型库nxdbm加载、连接数据库成功");

	return true;
}

/**
* @brief         读取系统基本配置
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__ReadBaseCfg()
{
	char cError[255]      = "";
	LIST_BASIC_CFG    tempList;

	if( NULL == m_pDbmModel )
		return false;

	m_pDbmModel->nxmodel_get_basic_cfg(tempList);

	if( tempList.size() <= 0 )
	{
		sprintf(cError,"nxmodel_get_basic_cfg()返回结果中记录数为%d,读取基本配置失败",tempList.size());
		RecordErrorLog(cError);
		return false;
	}
	m_BasicCfg = tempList.front();
	tempList.clear();

	RecordTraceLog("__ReadBaseCfg()读取系统基本配置成功");
	return true;
}

/**
* @brief         读取系统超时配置
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__ReadTimeOutCfg()
{
	char cError[255] = "";
	TIMEOUT_CFG_TB * pTmOutCfg = NULL;

	if( NULL == m_pDbmModel )
		return false;

	m_TimeOutList.clear();
	m_TimeOutMap.clear();

	// 读取配置
	m_pDbmModel->nxmodel_get_timeout_cfg(m_TimeOutList);
	int nListSize = m_TimeOutList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"nxmodel_get_timeout_cfg()返回结果中记录数为%d,读取超时配置失败",nListSize);
		RecordErrorLog(cError);
		//return false; //无超时配置不影响程序运行
	}
	else
	{
		sprintf(cError,"读取超时配置%d条记录成功",nListSize);
		RecordTraceLog(cError);
	}

	// 将记录映射到map表
	LIST_TIMEOUT::iterator ite = m_TimeOutList.begin();
	while( ite != m_TimeOutList.end())
	{
		pTmOutCfg = (TIMEOUT_CFG_TB *)(&(*ite));

		if( pTmOutCfg != NULL)
		{
			m_TimeOutMap[pTmOutCfg->e_obj_id] = pTmOutCfg;
			sprintf(cError,"超时映射map表增加功能ID=%d、value=%x的记录成功",pTmOutCfg->e_obj_id,pTmOutCfg);
			RecordTraceLog(cError);
		}

		pTmOutCfg = NULL;
		++ite;
	}

	// 映射记录合法性判断
	int nMapSize  = m_TimeOutMap.size();
	if( nMapSize != nListSize )
	{
		sprintf(cError,"数据库读取超时记录%d条，映射到map表记录%d条,不一致",nListSize,nMapSize);
		RecordErrorLog(cError);
		//return false;
	}
	return true;
}

/**
* @brief         读取对外通信规约配置
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__ReadProtocolCfg()
{
	char cError[255] = "";
	PROTOCOL_CFG_TB * pProCfg = NULL;
	PROTOCOL_CFG_TB   tempPro;
	LIST_PROTOCOL     ProList;

	if( NULL == m_pDbmModel )
		return false;

	CAutoLockOnStack tempLock(&m_LockForProtocol);

	// 读取配置
	m_pDbmModel->nxmodel_get_protocol(ProList);
	int nListSize = ProList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"nxmodel_get_protocol()返回结果中记录数为%d,读取规约配置失败",nListSize);
		RecordErrorLog(cError);
		return false;
	}

	m_ProtocolList.clear();
	m_ProMap.clear();

	// 获得对外通信规约配置
	while( ProList.size() > 0)
	{
		tempPro = ProList.front();
		if( tempPro.n_workrole == 2 /*信息远传*/ )
			m_ProtocolList.push_back(tempPro);
		ProList.pop_front();
	}

	nListSize = m_ProtocolList.size();
	sprintf(cError,"读取对外通信规约配置%d条记录",nListSize);
	RecordErrorLog(cError);
	if(nListSize <= 0)
		return false;

	// 将记录映射到map表
	LIST_PROTOCOL::iterator ite = m_ProtocolList.begin();
	while( ite != m_ProtocolList.end())
	{
		pProCfg = (PROTOCOL_CFG_TB *)(&(*ite));
		if( pProCfg != NULL)
		{
			m_ProMap[pProCfg->n_obj_id] = pProCfg;
			sprintf(cError,"规约映射map表增加ID=%d、value=%x的记录成功",pProCfg->n_obj_id,pProCfg);
			RecordTraceLog(cError);
		}

		pProCfg = NULL;
		++ite;
	}

	// 映射记录合法性判断
	int nMapSize  = m_ProMap.size();
	if( ( nMapSize != nListSize ) || (0 == nMapSize) || ( 0 == nListSize) )
	{
		sprintf(cError,"数据库读取对外通信规约记录%d条，映射到map表记录%d条,不一致或为0",nListSize,nMapSize);
		RecordErrorLog(cError);
		return false;
	}

	return true;
}

/**
* @brief         读取通信状态原因配置
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__ReadComuStatusChgReasonCfg()
{
	char cError[255] = "";
	CMU_CHG_RSN_TB * pCfg = NULL;

	if( NULL == m_pDbmModel )
		return false;

	m_ComuStatusReasonList.clear();
	m_ComuStatusReasonMap.clear();

	// 读取配置
	m_pDbmModel->nxmodel_get_cmu_chg_rsn(m_ComuStatusReasonList);
	int nListSize = m_ComuStatusReasonList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"nxmodel_get_cmu_chg_rsn()返回结果中记录数为%d,读取原因配置失败",nListSize);
		RecordErrorLog(cError);
		//return false; // 无状态原因不影响系统运行
	}
	else
	{
		sprintf(cError,"读取通信状态变化原因%d条记录成功",nListSize);
		RecordTraceLog(cError);
	}

	// 将记录映射到map表
	LIST_CMU_CHG_RSN::iterator ite = m_ComuStatusReasonList.begin();
	while( ite != m_ComuStatusReasonList.end())
	{
		pCfg = (CMU_CHG_RSN_TB *)(&(*ite));

		if( pCfg != NULL)
		{
			m_ComuStatusReasonMap[pCfg->e_obj_id] = pCfg;
			sprintf(cError,"原因映射map表增加ID=%d、value=%x的记录成功",pCfg->e_obj_id,pCfg);
			RecordTraceLog(cError);
		}

		pCfg = NULL;
		++ite;
	}

	// 映射记录合法性判断
	int nMapSize  = m_ComuStatusReasonMap.size();
	if( nMapSize != nListSize )
	{
		sprintf(cError,"数据库读取原因记录%d条，映射到map表记录%d条,不一致",nListSize,nMapSize);
		RecordErrorLog(cError);
		//return false;
	}
	return true;
}

/**
* @brief         读取客户端配置
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__ReadClientCfg()
{
	char cError[255] = "";

	if( NULL == m_pDbmModel )
		return false;

	CAutoLockOnStack tempLock(&m_LockForClient);

	// 读取配置
	m_pDbmModel->nxmodel_get_client(m_ClientList);
	int nListSize = m_ClientList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"nxmodel_get_client()返回结果中记录数为%d,读取客户端配置失败",nListSize);
		RecordErrorLog(cError);
		return false; 
	}
	sprintf(cError,"读取客户端配置%d条记录成功",nListSize);
	RecordTraceLog(cError);

	// 读取每个客户端的子配置
	LIST_CLIENT::iterator ite = m_ClientList.begin();
	while( ite != m_ClientList.end())
	{
		// 读通道配置
		m_pDbmModel->nxmodel_get_channel(ite->n_obj_id,ite->v_ChannelList);
		if( ite->v_ChannelList.size() <= 0 )
		{
			sprintf(cError,"ID=%d,NAME=%s的客户端无通道配置",ite->n_obj_id,ite->str_aliasname.c_str());
			RecordErrorLog(cError);
		}

		// 读信息订阅配置
		m_pDbmModel->nxmodel_get_order(ite->n_obj_id,ite->v_MsgTypeOrderList);

		// 读取不订阅设备配置
		m_pDbmModel->nxmodel_get_not_order(ite->n_obj_id,ite->v_NotOrderDevList);

		// 获得客户端的规约配置
		ite->p_backup = __GetProtocolCfg(ite->n_pro_obj);

		if( NULL == ite->p_backup )
		{
			sprintf(cError,"ID=%d,NAME=%s的客户端使用的PROID=%d的规约信息不存在",
				ite->n_obj_id,ite->str_aliasname.c_str(),ite->n_pro_obj);
			RecordErrorLog(cError);
		}

		sprintf(cError,"客户端(ID=%d,NAME=%s)的通道配置%d条,信息订阅%d条,不订阅设备%d条",
			ite->n_obj_id,ite->str_aliasname.c_str(),ite->v_ChannelList.size(),
			ite->v_MsgTypeOrderList.size(),ite->v_NotOrderDevList.size());

		RecordTraceLog(cError);

		++ite;
	}

	return true;
}

/**
* @brief         获取指定编号的对外通信规约配置
* @param[in]     UINT nProId:规约编号
* @param[out]    无
* @return        PROTOCOL_CFG_TB*:规约配置信息
*/
PROTOCOL_CFG_TB * CNXEcModelMgr::__GetProtocolCfg(UINT nProId)
{
	char cError[255] = "";
	CAutoLockOnStack tempLock(&m_LockForProtocol);

	EC_ID2PRO_MAP::iterator iteMap = m_ProMap.find(nProId);
	if( iteMap != m_ProMap.end())
	{
		return iteMap->second;
	}
	else
	{
		sprintf(cError,"找不到PROID=%d的规约信息",nProId);
		RecordErrorLog(cError);
	}
	return NULL;
}

/**
* @brief         读取监听配置
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__ReadListenCfg()
{
	char cError[255] = "";

	if( NULL == m_pDbmModel )
		return false;

	m_ListenList.clear();

	// 读取配置
	m_pDbmModel->nxmodel_get_listen(m_ListenList);
	int nListSize = m_ListenList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"nxmodel_get_listen()返回结果中记录数为%d,读取监听端口配置失败",nListSize);
		RecordErrorLog(cError);
		return false; 
	}
	sprintf(cError,"TOTAL:读取监听端口%d条记录成功",nListSize);
	RecordTraceLog(cError);
	return true;
}

/**
* @brief         重新读取及初始化指定变电站的模型
* @param[in]     UINT nStationID:指定变电站编号
* @param[out]    无
* @return        true :true-成功 false-失败
*/
bool CNXEcModelMgr::RefreshSubStationModel(UINT nStationID)
{
	char cError[255] = "";
	LIST_SUBSTATION    StationList;     // 变电站基本信息链表
	LIST_PRIM          AllPrimList;     // 所有一次设备信息链表 
	LIST_IED           AllIedList;      // 所有IED信息链表

	if( NULL == m_pDbmModel )
		return false;

	// 读取全部一次设备配置
	__ReadAllPrimDevCfg(AllPrimList);

	// 读取全部二次设备配置
	__ReadAllIedBasicCfg(AllIedList);

	// 读取变电站配置
	__ReadAllSubStationBasicCfg(StationList);

	// 读取各变电站子属配置并将记录映射到map表
	bool bfind                     = false;         // 标识是否找到指定的变电站ID
	EC_SUBSTATION *    pNewStation = NULL;
	SUBSTATION_TB      tempStation ;
	while( StationList.size() > 0 )
	{
		// 取首站
		tempStation = StationList.front();
		if( (!bfind) && ( tempStation.n_obj_id == nStationID ) )
		{
			// 分配空间
			pNewStation = new EC_SUBSTATION;
			// 变电站基本信息
			pNewStation->pStation = new SUBSTATION_TB(tempStation);
			bfind = true;
		}
		tempStation.lst_bay.clear();
		StationList.pop_front();
	}

	if( !bfind )
	{
		sprintf(cError,"数据库配置中没有变电站ID=%d的信息,RefreshSubStationModel()失败",nStationID);
		RecordErrorLog(cError);
		//　清除临时一、二次设备链表
		AllPrimList.clear();
		while (AllIedList.size() > 0)
		{
			IED_TB tempIed = AllIedList.front();
			tempIed.v_ld.clear();
			AllIedList.pop_front();
		}
		return false;
	}

	// 从总一次设备链表中获取本变电站一次设备
	___AddSubStationPrimDev( nStationID ,AllPrimList,pNewStation->PrimList,pNewStation->PrimMap);

	// 从总IED链表中获取本变电站IED并设置与一次设备的关联关系
	___AddSubStationIed( nStationID,AllIedList,pNewStation->PrimMap,pNewStation->IedList,pNewStation->IedMap);

	// 首先删除原先结点
	___RemoveSubStatonFromMap(nStationID);

	// 将变电站信息更新入map表
	___UpdateSubStatonToMap( pNewStation );

	sprintf(cError,"END:重新读取变电站(obj_id=%d)及其所有子属性全部完成",nStationID);
	RecordTraceLog(cError);

	//　清除临时一、二次设备链表
	AllPrimList.clear();
	while (AllIedList.size() > 0)
	{
		IED_TB tempIed = AllIedList.front();
		tempIed.v_ld.clear();
		AllIedList.pop_front();
	}

	// 重新更新IED状态列表(读取不带LD的IED配置)
	CAutoLockOnStack IedLock(&m_LockForIed);
	__ReadAllIedBasicCfg(m_IedList,false);

	return true;
}

/**
* @brief         读取所有变电站及其下属全部配置
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__ReadAllSubStationWholeCfg()
{
	char cError[255] = "";
	LIST_SUBSTATION    StationList;     // 变电站基本信息链表
	LIST_PRIM          AllPrimList;     // 所有一次设备信息链表 
	LIST_IED           AllIedList;      // 所有IED信息链表

	if( NULL == m_pDbmModel )
		return false;

	// 读取全部一次设备配置
	if(!__ReadAllPrimDevCfg(AllPrimList))
		return false;

	// 读取全部二次设备配置
	if(!__ReadAllIedBasicCfg(AllIedList))
		return false;

	// 读取变电站配置
	if(!__ReadAllSubStationBasicCfg(StationList))
		return false;

	// 读取各变电站子属配置并将记录映射到map表
	EC_SUBSTATION *    pNewStation = NULL;
	int                nStationId  = 0;
	SUBSTATION_TB      tempStation ;
	while( StationList.size() > 0 )
	{
		// 取首站
		tempStation = StationList.front();

		// 分配空间并设置子属性
		pNewStation = new EC_SUBSTATION;    

		// 变电站基本信息
		pNewStation->pStation = new SUBSTATION_TB(tempStation);
		nStationId            = pNewStation->pStation->n_obj_id;

		// 从总一次设备链表中获取本变电站一次设备
		___AddSubStationPrimDev( nStationId ,AllPrimList,pNewStation->PrimList,pNewStation->PrimMap);

		// 从总IED链表中获取本变电站IED并设置与一次设备的关联关系
		___AddSubStationIed( nStationId,AllIedList,pNewStation->PrimMap,pNewStation->IedList,pNewStation->IedMap);

		// 将变电站信息更新入map表
		___UpdateSubStatonToMap( pNewStation );

		// 清除间隔信息
		tempStation.lst_bay.clear();

		// 删除头元素 
		StationList.pop_front();
		pNewStation = NULL;
	}

	RecordTraceLog("END:读取所有变电站及其所有子属性全部完成");

	//　清除临时一、二次设备链表
	AllPrimList.clear();
	while (AllIedList.size() > 0)
	{
		IED_TB tempIed = AllIedList.front();
		tempIed.v_ld.clear();
		AllIedList.pop_front();
	}
	return true;
}

/**
* @brief         从指定一次设备链表中获取指定站点的一次设备信息
* @param[in]     nStationID :变电站ID
* @param[in]     AllPrimDevList ：全部一次设备链表
* @param[out]    PrimList:指定变电站的一次设备链表
* @param[out]    PrimMap:指定变电站的一次设备map表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::___AddSubStationPrimDev(IN UINT nStationId,IN LIST_PRIM& AllPrimDevList,OUT EC_PRIM_LIST& PrimList,OUT EC_PRIM_MAP& PrimMap)
{
	char cError[255] = "";           
	PRIMEQUIPMENT_TB * pCfg = NULL;

	LIST_PRIM::iterator ite = AllPrimDevList.begin();
	while( ite != AllPrimDevList.end())
	{
		if( ite->n_station_obj != nStationId )
		{
			++ ite;
			continue;
		}

		// 构造设备配置对象
		pCfg = new PRIMEQUIPMENT_TB(*ite);

		// 加入设备链表
		PrimList.push_back(pCfg);

		// 加入设备映射表
		PrimMap[pCfg->n_obj_id] = pCfg;

		sprintf(cError,"变电站(ID=%d)中增加ID=%d、value=%x的一次设备记录成功",nStationId,pCfg->n_obj_id,pCfg);
		RecordTraceLog(cError);

		pCfg = NULL;
		++ite;
	}

	sprintf(cError,"TOTAL:变电站(ID=%d)一次设备映射表中增加%d条记录成功",nStationId,PrimMap.size());
	RecordTraceLog(cError);
	return true;
}

/**
* @brief         从指定IED设备链表中获取指定站点IED设备信息
* @param[in]     nStationID :变电站ID
* @param[in]     AllIEDList ：全部IED设备链表
* @param[in]     EC_PRIM_MAP & PrimMap :一次设备Map表
* @param[out]    IedList:指定变电站的IED设备链表
* @param[out]    IedMap:指定变电站的IED设备map表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::___AddSubStationIed(IN UINT nStationId,IN LIST_IED& AllIEDList,IN EC_PRIM_MAP & PrimMap,
	OUT EC_IED_LIST&IedList,OUT EC_IED_MAP&IedMap)
{
	char cError[255] = "";   
	char cMapId[100] = "";
	EC_IED * pCfg = NULL;
	IED_TB * pIedTb = NULL;
	EC_PRIM_MAP::iterator prMapIte;

	LIST_IED::iterator ite = AllIEDList.begin();
	while( ite != AllIEDList.end())
	{
		if( ite->n_station_obj != nStationId )
		{
			++ ite;
			continue;
		}

		// 构造设备配置对象
		pCfg = new EC_IED;
		pIedTb = new IED_TB(*ite);
		pCfg->pIed = pIedTb;

		// 读取IED的子配置属性
		____ReadIedSubProperty(pCfg);

		// 设置IED关联的一次设备
		prMapIte = PrimMap.find(pIedTb->n_primequ_obj);
		if( prMapIte != PrimMap.end())
		{
			pCfg->pPrimDevCfg = prMapIte->second;
			sprintf(cError,"IED(ID=%d)关联一次设备(ID=%d,value=%x)设置完毕",
				pIedTb->n_obj_id,prMapIte->second->n_obj_id,prMapIte->second);
			RecordTraceLog(cError);
		}

		// 加入设备链表
		IedList.push_back(pCfg);

		// 将记录添加到map表  // 键值:IED_ID,VALUE:IED_TB *
		_ZERO_MEM(cMapId,100);
		sprintf(cMapId,"%d",pIedTb->n_obj_id);
		IedMap.insert( make_pair(cMapId,pCfg) ); 

		// 键值:厂站编号/ied103地址 VALUE:IED_TB* 
		_ZERO_MEM(cMapId,100);
		sprintf(cMapId,"%d/%d",nStationId,pIedTb->n_outaddr103); 
		IedMap.insert( make_pair(cMapId,pCfg) ); 

		sprintf(cError,"变电站(ID=%d)中增加ID=%d、value=%x的IED设备记录成功",nStationId,pIedTb->n_obj_id,pCfg);
		RecordTraceLog(cError);

		pCfg   = NULL;
		pIedTb = NULL;
		++ite;
	}

	sprintf(cError,"TOTAL:变电站(ID=%d)IED映射表中增加%d条记录成功",nStationId,IedMap.size());
	RecordTraceLog(cError);
	return true;
}

/**
* @brief         将变电站信息指针加入变电站map表
* @param[in]     EC_SUBSTATION * :变电站结构指针
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::___UpdateSubStatonToMap( EC_SUBSTATION * pStation )
{
	char cError[255] = "";
	CAutoLockOnStack tempLock(&m_LockForSubStation);

	int nStationID = pStation->pStation->n_obj_id;

	// 如果已存在则更新
	m_SubStationMap[nStationID] = pStation;

	sprintf(cError,"变电站映射表中增加ID=%d、value=%x的变电站记录成功",nStationID,pStation);
	RecordTraceLog(cError);

	return true;
}

/**
* @brief         从变电站map表中删除指定的结点及其资源
* @param[in]     UINT nStationID :变电站编号
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::___RemoveSubStatonFromMap( UINT nStationID )
{
	char cError[255] = "";
	EC_SUBSTATION * pStation = NULL;
	CAutoLockOnStack tempLock(&m_LockForSubStation);

	EC_SUBSTATION_MAP::iterator ite = m_SubStationMap.find(nStationID);
	if( ite != m_SubStationMap.end() )
	{
		pStation = ite->second;
		if( NULL != pStation )
		{
			// 删除该变电站下的资源
			___FreeSubStationCfg(pStation);
			delete pStation;
			pStation = NULL;
		}
		m_SubStationMap.erase(ite);
	}
	return true;
}

/**
* @brief         读取所有变电站基本配置(仅含间隔)
* @param[in]     无
* @param[out]    LIST_SUBSTATION & StationList
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__ReadAllSubStationBasicCfg(LIST_SUBSTATION & StationList)
{
	char cError[255] = "";

	if( NULL == m_pDbmModel )
		return false;

	// 读取配置
	m_pDbmModel->nxmodel_get_substation_whole(StationList);
	int nListSize = StationList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"nxmodel_get_substation_whole()返回结果中记录数为%d,读取变电站配置失败",nListSize);
		RecordErrorLog(cError);
		return false; 
	}
	sprintf(cError,"读取全部变电站记录数为%d",nListSize);
	RecordTraceLog(cError);
	return true;
}

/**
* @brief         读取所有一次设备配置
* @param[out]    LIST_PRIM & PrimList: 保存一次设备链表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__ReadAllPrimDevCfg(LIST_PRIM & PrimList)
{
	char cError[255] = "";

	if( NULL == m_pDbmModel )
		return false;

	// 读取配置
	m_pDbmModel->nxmodel_get_all_prim(PrimList);
	int nListSize = PrimList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"nxmodel_get_all_prim()返回结果中记录数为%d,读取一次设备配置失败",nListSize);
		RecordErrorLog(cError);
		return false; 
	}
	sprintf(cError,"读取全部一次设备记录数为%d",nListSize);
	RecordTraceLog(cError);
	return true;
}

/**
* @brief         读取所有IED设备配置(仅含LD,不含LD下的信息点表)
* @param[OUT]    LIST_IED& IedList: 保存IED设备链表
* @param[IN]     bool bHaveLd: 是否带IED的LD基本配置(默认为带:true)
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__ReadAllIedBasicCfg(OUT LIST_IED& IedList,bool bHaveLd)
{
	char cError[255] = "";

	if( NULL == m_pDbmModel )
		return false;

	IedList.clear();

	// 读取IED及其LD基本配置
	m_pDbmModel->nxmodel_get_all_ied_ld(IedList);

	int nListSize = IedList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"nxmodel_get_all_ied_ld()返回结果中记录数为%d,读取IED设备配置失败",nListSize);
		RecordErrorLog(cError);
		return false; 
	}

	// 如果不需要CPU则清除
	if( !bHaveLd )
	{
		LIST_IED::iterator ite = IedList.begin();
		while ( ite != IedList.end() )
		{
			ite->v_ld.clear();
			++ite;
		}
	}

	sprintf(cError,"读取全部IED记录数为%d",nListSize);
	RecordTraceLog(cError);

	return true;
}

/**
* @brief         读取指定IED设备的子属性
* @param[in]     EC_IED * pIedCfg :IED结构指针
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::____ReadIedSubProperty( IN OUT EC_IED * pIedCfg)
{
	char cError[255] = "";
	EC_LD * pEcLD = NULL;
	//LD_TB   tempLD ;
	LIST_LD::iterator ite = pIedCfg->pIed->v_ld.begin();

	//while ( pIedCfg->pIed->v_ld.size() > 0 )
	while(ite != pIedCfg->pIed->v_ld.end())
	{
		// 取头元素
		//tempLD = pIedCfg->pIed->v_ld.front();

		// 构造LD对象
		pEcLD = new EC_LD;
		//pEcLD->pLdCfg = new LD_TB(tempLD);
		pEcLD->pLdCfg = new LD_TB(*ite);

		// 读取LD定值属性并设置map映射表
		_____ReadLD_SGCfg(pIedCfg->pIed->n_obj_id,pEcLD->pLdCfg->n_ld_code,pEcLD->pLdCfg->v_sg,pIedCfg->sgMap);

		// 读取LD AI属性并设置map映射表
		_____ReadLD_AICfg(pIedCfg->pIed->n_obj_id,pEcLD->pLdCfg->n_ld_code,pEcLD->pLdCfg->v_analog,pIedCfg->aiMap);

		// 读取LD DI属性并设置map映射表
		_____ReadLD_DICfg(pIedCfg->pIed->n_obj_id,pEcLD->pLdCfg->n_ld_code,pEcLD->pLdCfg->v_hardstrap,pIedCfg->diMap);

		// 读取LD SOFT属性并设置map映射表
		_____ReadLD_SoftCfg(pIedCfg->pIed->n_obj_id,pEcLD->pLdCfg->n_ld_code,pEcLD->pLdCfg->v_softstrap,pIedCfg->softMap);

		// 读取LD 事件属性并设置map映射表
		_____ReadLD_EventCfg(pIedCfg->pIed->n_obj_id,pEcLD->pLdCfg->n_ld_code,pEcLD->pLdCfg->v_event,pIedCfg->eventMap);

		// 读取LD 告警属性并设置map映射表
		_____ReadLD_AlarmCfg(pIedCfg->pIed->n_obj_id,pEcLD->pLdCfg->n_ld_code,pEcLD->pLdCfg->v_alarm,pIedCfg->alramMap);

		// 读取故障量属性并设置map映射表
		_____ReadLD_FaultCfg(pIedCfg->pIed->n_obj_id,pEcLD->pLdCfg->n_ld_code,pEcLD->pLdCfg->v_faulttag,pIedCfg->faultMap);

		// 读取定值区号属性并设置map映射表
		_____ReadLD_ZoneCfg(pIedCfg->pIed->n_obj_id,pEcLD->pLdCfg->n_ld_code,pEcLD->pLdCfg->v_sgzone,pIedCfg->zoneMap);

		// 读取LD下的group title配置并设置map映射表
		_____ReadLD_GTitleCfg(pIedCfg->pIed->n_obj_id,pEcLD->pLdCfg->n_ld_code,pEcLD->vGTitleList,pIedCfg->groupMap);

		// 读取LD下录波模拟量通道
		_____ReadLD_OscAiCfg(pIedCfg->pIed->n_obj_id,pEcLD->pLdCfg->n_ld_code,pEcLD->pLdCfg->v_osc_ai,pIedCfg->osc_aiMap);

		// 读取LD下录波开关量通道
		_____ReadLD_OscDiCfg(pIedCfg->pIed->n_obj_id,pEcLD->pLdCfg->n_ld_code,pEcLD->pLdCfg->v_osc_di,pIedCfg->osc_diMap);

		// 加入LD 链表
		pIedCfg->lDList.push_back(pEcLD);

		// 设置LD　Map映射表
		pIedCfg->ldMap[pEcLD->pLdCfg->n_ld_code] = pEcLD;

#ifdef _DEBUG
		sprintf(cError,"IED设备(ID=%d)中增加ID=%d、value=%x的LD记录成功",
			pIedCfg->pIed->n_obj_id,pEcLD->pLdCfg->n_ld_code,pEcLD);
		RecordTraceLog(cError);
#endif
		// 删除头元素
		//pIedCfg->pIed->v_ld.pop_front();
		++ite;
		pEcLD = NULL;
	}

	sprintf(cError,"TOTAL:IED(ID=%d)的LD映射表中增加%d条记录成功",
		pIedCfg->pIed->n_obj_id,pIedCfg->ldMap.size());
	RecordTraceLog(cError);

	return true;
}

/**
* @brief         读取LD定值属性并设置map映射表
* @param[in]     int nIedID:ied编号
* @param[in]     int nLdID: ld编号
* @param[in]     LIST_SG & sgList : 保存定值信息的链表
* @param[in]     EC_SG_MAP & sgMap :定值映射表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_____ReadLD_SGCfg(int nIedId,int nLdId,LIST_SG & sgList,EC_SG_MAP & sgMap)
{
	char cError[255] = "";
	char cMapID[100] = "";
	SG_TB * pSgCfg   = NULL;

	if( NULL == m_pDbmModel )
		return false;

	// 读取LD下的定值配置
	m_pDbmModel->nxmodel_get_sg(nIedId,nLdId,sgList);
	int nListSize = sgList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"读取IED=%d,LD=%d设备的定值，nxmodel_get_sg()返回记录数为%d",nIedId,nLdId,nListSize);
		RecordErrorLog(cError);
		return false;
	}
	sprintf(cError,"读取IED=%d,LD=%d设备的定值记录数为%d",nIedId,nLdId,nListSize);
	RecordTraceLog(cError);

	// 遍历记录并增加到map映射表
	LIST_SG::iterator sgListIte = sgList.begin();
	while( sgListIte != sgList.end() )
	{
		pSgCfg = (SG_TB *)(&(*sgListIte));

		// 加入map 主键:"LD编号/定值编号",值:SG_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/%d",nLdId,pSgCfg->n_sg_code);
		sgMap.insert( make_pair(cMapID,pSgCfg) );

		// 加入map 主键："LD编号/G组号/条目号"，值:SG_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/G%d/%d",nLdId,pSgCfg->n_outgroup103,pSgCfg->n_outitem103);
		sgMap.insert( make_pair(cMapID,pSgCfg) );

#ifdef _DEBUG
		sprintf(cError,"IED(ID=%d,LD=%d)的映射表中增加ID=%d、value=%x定值记录成功",
			nIedId,nLdId,pSgCfg->n_sg_code,pSgCfg);
		RecordTraceLog(cError);
#endif
		pSgCfg = NULL;
		++sgListIte;
	}

	sprintf(cError,"TOTAL:IED(ID=%d,LD=%d)的定值映射表中增加%d条记录成功",
		nIedId,nLdId,sgMap.size());
	RecordTraceLog(cError);

	return true;
}

/**
* @brief         读取LD AI属性并设置map映射表
* @param[in]     int nIedID:ied编号
* @param[in]     int nLdID: ld编号
* @param[in]     LIST_ANALOG& aiList : 保存信息的链表
* @param[in]     EC_AI_MAP& aiMap :    信息映射表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_____ReadLD_AICfg(int nIedId,int nLdId,LIST_ANALOG& aiList,EC_AI_MAP& aiMap)
{
	char cError[255] = "";
	char cMapID[100] = "";
	ANALOG_TB * pAiCfg   = NULL;

	if( NULL == m_pDbmModel )
		return false;

	// 读取LD下的模拟量配置
	m_pDbmModel->nxmodel_get_analog(nIedId,nLdId,aiList);
	int nListSize = aiList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"读取IED=%d,LD=%d设备的模拟量，nxmodel_get_analog()返回记录数为%d",nIedId,nLdId,nListSize);
		RecordErrorLog(cError);
		return false;
	}
	sprintf(cError,"读取IED=%d,LD=%d设备的模拟量记录数为%d",nIedId,nLdId,nListSize);
	RecordTraceLog(cError);

	// 遍历记录并增加到map映射表
	LIST_ANALOG::iterator ListIte = aiList.begin();
	while( ListIte != aiList.end() )
	{
		pAiCfg = (ANALOG_TB *)(&(*ListIte));

		// 加入map 主键:"LD编号/AI编号",值:ANALOG_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/%d",nLdId,pAiCfg->n_ana_code);
		aiMap.insert( make_pair(cMapID,pAiCfg) );

		// 加入map 主键："LD编号/G组号/条目号"，值:ANALOG_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/G%d/%d",nLdId,pAiCfg->n_outgroup103,pAiCfg->n_outitem103);
		aiMap.insert( make_pair(cMapID,pAiCfg) );

#ifdef _DEBUG
		sprintf(cError,"IED(ID=%d,LD=%d)的映射表中增加ID=%d、value=%x模拟量记录成功",
			nIedId,nLdId,pAiCfg->n_ana_code,pAiCfg);
		RecordTraceLog(cError);
#endif
		pAiCfg = NULL;
		++ListIte;
	}

	sprintf(cError,"TOTAL:IED(ID=%d,LD=%d)的模拟量映射表中增加%d条记录成功",
		nIedId,nLdId,aiMap.size());
	RecordTraceLog(cError);
	return true;
}

/**
* @brief         读取LD DI属性并设置map映射表
* @param[in]     int nIedID:ied编号
* @param[in]     int nLdID: ld编号
* @param[in]     LIST_STRAP & diLis : 保存信息的链表
* @param[in]     EC_DI_MAP & diMap :  信息映射表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_____ReadLD_DICfg(int nIedId,int nLdId,LIST_STRAP & diList,EC_DI_MAP & diMap)
{
	char cError[255] = "";
	char cMapID[100] = "";
	STRAP_TB* pCfg   = NULL;

	if( NULL == m_pDbmModel )
		return false;

	// 读取LD下的开关量配置
	m_pDbmModel->nxmodel_get_hardstrap(nIedId,nLdId,diList);
	int nListSize = diList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"读取IED=%d,LD=%d设备的开关量，nxmodel_get_hardstrap()返回记录数为%d",nIedId,nLdId,nListSize);
		RecordErrorLog(cError);
		return false;
	}
	sprintf(cError,"读取IED=%d,LD=%d设备的开关量记录数为%d",nIedId,nLdId,nListSize);
	RecordTraceLog(cError);

	// 遍历记录并增加到map映射表
	LIST_STRAP::iterator ListIte = diList.begin();
	while( ListIte != diList.end() )
	{
		pCfg = (STRAP_TB *)(&(*ListIte));

		// 加入map 主键:"LD编号/DI编号",值:STRAP_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/%d",nLdId,pCfg->n_strap_code);
		diMap.insert( make_pair(cMapID,pCfg) );

		// 加入map 主键："LD编号/G组号/条目号"，值:STRAP_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/G%d/%d",nLdId,pCfg->n_outgroup103,pCfg->n_outitem103);
		diMap.insert( make_pair(cMapID,pCfg) );

		// 加入map 主键：""LD编号/F功能类型/信息序号"，值:STRAP_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/F%d/%d",nLdId,pCfg->n_outfun103,pCfg->n_outinf103);
		diMap.insert( make_pair(cMapID,pCfg) );

#ifdef _DEBUG
		sprintf(cError,"IED(ID=%d,LD=%d)的映射表中增加ID=%d、value=%x开关量记录成功",
			nIedId,nLdId,pCfg->n_strap_code,pCfg);
		RecordTraceLog(cError);
#endif
		pCfg = NULL;
		++ListIte;
	}
	sprintf(cError,"TOTAL:IED(ID=%d,LD=%d)的开关量映射表中增加%d条记录成功",
		nIedId,nLdId,diMap.size());
	RecordTraceLog(cError);
	return true;
}

/**
* @brief         读取LD SOFT属性并设置map映射表
* @param[in]     int nIedID:ied编号
* @param[in]     int nLdID: ld编号
* @param[in]     LIST_STRAP & softList : 保存信息的链表
* @param[in]     EC_SOFTSTRAP_MAP& softMap :  信息映射表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_____ReadLD_SoftCfg(int nIedId,int nLdId,LIST_STRAP & softList,EC_SOFTSTRAP_MAP& softMap)
{
	char cError[255] = "";
	char cMapID[100] = "";
	STRAP_TB* pCfg   = NULL;

	if( NULL == m_pDbmModel )
		return false;

	// 读取LD下的软压板配置
	m_pDbmModel->nxmodel_get_softstrap(nIedId,nLdId,softList);
	int nListSize = softList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"读取IED=%d,LD=%d设备的软压板，nxmodel_get_softstrap()返回记录数为%d",nIedId,nLdId,nListSize);
		RecordErrorLog(cError);
		return false;
	}
	sprintf(cError,"读取IED=%d,LD=%d设备的软压板记录数为%d",nIedId,nLdId,nListSize);
	RecordTraceLog(cError);

	// 遍历记录并增加到map映射表
	LIST_STRAP::iterator ListIte = softList.begin();
	while( ListIte != softList.end() )
	{
		pCfg = (STRAP_TB *)(&(*ListIte));

		// 加入map 主键:"LD编号/Soft编号",值:STRAP_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/%d",nLdId,pCfg->n_strap_code);
		softMap.insert( make_pair(cMapID,pCfg) );

		// 加入map 主键："LD编号/G组号/条目号"，值:STRAP_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/G%d/%d",nLdId,pCfg->n_outgroup103,pCfg->n_outitem103);
		softMap.insert( make_pair(cMapID,pCfg) );

		// 加入map 主键：""LD编号/F功能类型/信息序号"，值:STRAP_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/F%d/%d",nLdId,pCfg->n_outfun103,pCfg->n_outinf103);
		softMap.insert( make_pair(cMapID,pCfg) );

#ifdef _DEBUG
		sprintf(cError,"IED(ID=%d,LD=%d)的映射表中增加ID=%d、value=%x软压板记录成功",
			nIedId,nLdId,pCfg->n_strap_code,pCfg);
		RecordTraceLog(cError);
#endif
		pCfg = NULL;
		++ListIte;
	}

	sprintf(cError,"TOTAL:IED(ID=%d,LD=%d)的软压板映射表中增加%d条记录成功",
		nIedId,nLdId,softMap.size());
	RecordTraceLog(cError);
	return true;
}

/**
* @brief         读取LD 事件属性并设置map映射表
* @param[in]     int nIedID:ied编号
* @param[in]     int nLdID: ld编号
* @param[in]     LIST_EVENT & eventList : 保存信息的链表
* @param[in]     EC_EVENT_MAP &eventMap :  信息映射表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_____ReadLD_EventCfg(int nIedId,int nLdId,LIST_EVENT & eventList,EC_EVENT_MAP &eventMap)
{
	char cError[255] = "";
	char cMapID[100] = "";
	EVENT_TB* pCfg   = NULL;

	if( NULL == m_pDbmModel )
		return false;

	// 读取LD下的事件配置
	m_pDbmModel->nxmodel_get_event(nIedId,nLdId,eventList);
	int nListSize = eventList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"读取IED=%d,LD=%d设备的事件，nxmodel_get_event()返回记录数为%d",nIedId,nLdId,nListSize);
		RecordErrorLog(cError);
		return false;
	}
	sprintf(cError,"读取IED=%d,LD=%d设备的事件记录数为%d",nIedId,nLdId,nListSize);
	RecordTraceLog(cError);

	// 遍历记录并增加到map映射表
	LIST_EVENT::iterator ListIte = eventList.begin();
	while( ListIte != eventList.end() )
	{
		pCfg = (EVENT_TB *)(&(*ListIte));

		// 加入map 主键:"LD编号/event编号",值:EVENT_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/%d",nLdId,pCfg->n_event_code);
		eventMap.insert( make_pair(cMapID,pCfg) );

		// 加入map 主键："LD编号/G组号/条目号"，值:EVENT_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/G%d/%d",nLdId,pCfg->n_outgroup103,pCfg->n_outitem103);
		eventMap.insert( make_pair(cMapID,pCfg) );

		// 加入map 主键：""LD编号/F功能类型/信息序号"，值:EVENT_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/F%d/%d",nLdId,pCfg->n_outfun103,pCfg->n_outinf103);
		eventMap.insert( make_pair(cMapID,pCfg) );

#ifdef _DEBUG
		sprintf(cError,"IED(ID=%d,LD=%d)的映射表中增加ID=%d、value=%x事件记录成功",
			nIedId,nLdId,pCfg->n_event_code,pCfg);
		RecordTraceLog(cError);
#endif
		pCfg = NULL;
		++ListIte;
	}

	sprintf(cError,"TOTAL:IED(ID=%d,LD=%d)的事件映射表中增加%d条记录成功",
		nIedId,nLdId,eventMap.size());
	RecordTraceLog(cError);
	return true;
}

/**
* @brief         读取LD 告警属性并设置map映射表
* @param[in]     int nIedID:ied编号
* @param[in]     int nLdID: ld编号
* @param[in]     LIST_ALARM& alarmList : 保存信息的链表
* @param[in]     EC_ALARM_MAP&　alramMap :  信息映射表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_____ReadLD_AlarmCfg(int nIedId,int nLdId,LIST_ALARM& alarmList,EC_ALARM_MAP& alarmMap)
{
	char cError[255] = "";
	char cMapID[100] = "";
	ALARM_TB* pCfg   = NULL;

	if( NULL == m_pDbmModel )
		return false;

	// 读取LD下的告警配置
	m_pDbmModel->nxmodel_get_alarm(nIedId,nLdId,alarmList);
	int nListSize = alarmList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"读取IED=%d,LD=%d设备的告警，nxmodel_get_alarm()返回记录数为%d",nIedId,nLdId,nListSize);
		RecordErrorLog(cError);
		return false;
	}
	sprintf(cError,"读取IED=%d,LD=%d设备的告警记录数为%d",nIedId,nLdId,nListSize);
	RecordTraceLog(cError);

	// 遍历记录并增加到map映射表
	LIST_ALARM::iterator ListIte = alarmList.begin();
	while( ListIte != alarmList.end() )
	{
		pCfg = (ALARM_TB *)(&(*ListIte));

		// 加入map 主键:"LD编号/alarm编号",值:ALARM_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/%d",nLdId,pCfg->n_alarm_code);
		alarmMap.insert( make_pair(cMapID,pCfg) );

		// 加入map 主键："LD编号/G组号/条目号"，值:ALARM_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/G%d/%d",nLdId,pCfg->n_outgroup103,pCfg->n_outitem103);
		alarmMap.insert( make_pair(cMapID,pCfg) );

		// 加入map 主键：""LD编号/F功能类型/信息序号"，值:ALARM_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/F%d/%d",nLdId,pCfg->n_outfun103,pCfg->n_outinf103);
		alarmMap.insert( make_pair(cMapID,pCfg) );

#ifdef _DEBUG
		sprintf(cError,"IED(ID=%d,LD=%d)的映射表中增加ID=%d、value=%x告警记录成功",
			nIedId,nLdId,pCfg->n_alarm_code,pCfg);
		RecordTraceLog(cError);
#endif
		pCfg = NULL;
		++ListIte;
	}
	sprintf(cError,"TOTAL:IED(ID=%d,LD=%d)的告警映射表中增加%d条记录成功",
		nIedId,nLdId,alarmMap.size());
	RecordTraceLog(cError);
	return true;
}

/**
* @brief         读取故障量属性并设置map映射表
* @param[in]     int nIedID:ied编号
* @param[in]     int nLdID: ld编号
* @param[in]     LIST_FAULTTAG& faultList : 保存信息的链表
* @param[in]     EC_FAULT_MAP& faultMap :  信息映射表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_____ReadLD_FaultCfg(int nIedId,int nLdId,LIST_FAULTTAG& faultList,EC_FAULT_MAP& faultMap)
{
	char cError[255] = "";
	char cMapID[100] = "";
	FAULTTAG_TB* pCfg   = NULL;

	if( NULL == m_pDbmModel )
		return false;

	// 读取LD下的故障量配置
	m_pDbmModel->nxmodel_get_faulttag(nIedId,nLdId,faultList);
	int nListSize = faultList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"读取IED=%d,LD=%d设备的故障量,nxmodel_get_faulttag()返回记录数为%d",nIedId,nLdId,nListSize);
		RecordErrorLog(cError);
		return false;
	}
	sprintf(cError,"读取IED=%d,LD=%d设备的故障量记录数为%d",nIedId,nLdId,nListSize);
	RecordTraceLog(cError);

	// 遍历记录并增加到map映射表
	LIST_FAULTTAG::iterator ListIte = faultList.begin();
	while( ListIte != faultList.end() )
	{
		pCfg = (FAULTTAG_TB *)(&(*ListIte));

		// 加入map 主键:"LD编号/fault编号",值:FAULTTAG_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/%d",nLdId,pCfg->n_tag_code);
		faultMap.insert( make_pair(cMapID,pCfg) );

		// 加入map 主键："LD编号/G组号/条目号"，值:FAULTTAG_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/G%d/%d",nLdId,pCfg->n_outgroup103,pCfg->n_outitem103);
		faultMap.insert( make_pair(cMapID,pCfg) );

#ifdef _DEBUG
		sprintf(cError,"IED(ID=%d,LD=%d)的映射表中增加ID=%d、value=%x故障量记录成功",
			nIedId,nLdId,pCfg->n_tag_code,pCfg);
		RecordTraceLog(cError);
#endif
		pCfg = NULL;
		++ListIte;
	}
	sprintf(cError,"TOTAL:IED(ID=%d,LD=%d)的故障量映射表中增加%d条记录成功",
		nIedId,nLdId,faultMap.size());
	RecordTraceLog(cError);
	return true;
}

/**
* @brief         读取定值区号属性并设置map映射表
* @param[in]     int nIedID:ied编号
* @param[in]     int nLdID: ld编号
* @param[in]     LIST_SGZONE& zoneList : 保存信息的链表
* @param[in]     EC_ZONE_MAP &　zoneMap :  信息映射表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_____ReadLD_ZoneCfg(int nIedId,int nLdId,LIST_SGZONE& zoneList,EC_ZONE_MAP & zoneMap)
{
	char cError[255] = "";
	char cMapID[100] = "";
	SGZONE_TB* pCfg   = NULL;

	if( NULL == m_pDbmModel )
		return false;

	// 读取LD下的定值区配置
	m_pDbmModel->nxmodel_get_sgzone(nIedId,nLdId,zoneList);
	int nListSize = zoneList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"读取IED=%d,LD=%d设备的定值区号,nxmodel_get_sgzone()返回记录数为%d",nIedId,nLdId,nListSize);
		RecordErrorLog(cError);
		return false;
	}
	sprintf(cError,"读取IED=%d,LD=%d设备的定值区记录数为%d",nIedId,nLdId,nListSize);
	RecordTraceLog(cError);

	// 遍历记录并增加到map映射表
	LIST_SGZONE::iterator ListIte = zoneList.begin();
	while( ListIte != zoneList.end() )
	{
		pCfg = (SGZONE_TB *)(&(*ListIte));

		// 加入map 主键:"LD编号/zone编号",值:SGZONE_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/%d",nLdId,pCfg->n_zone_code);
		zoneMap.insert( make_pair(cMapID,pCfg) );

		// 加入map 主键："LD编号/G组号/条目号"，值:SGZONE_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/G%d/%d",nLdId,pCfg->n_outgroup103,pCfg->n_outitem103);
		zoneMap.insert( make_pair(cMapID,pCfg) );

#ifdef _DEBUG
		sprintf(cError,"IED(ID=%d,LD=%d)的映射表中增加ID=%d、value=%x定值区号记录成功",
			nIedId,nLdId,pCfg->n_zone_code,pCfg);
		RecordTraceLog(cError);
#endif
		pCfg = NULL;
		++ListIte;
	}
	sprintf(cError,"TOTAL:IED(ID=%d,LD=%d)的定值区映射表中增加%d条记录成功",
		nIedId,nLdId,zoneMap.size());
	RecordTraceLog(cError);
	return true;
}

/**
* @brief         读取LD下的group title配置并设置map映射表
* @param[in]     int nIedID:ied编号
* @param[in]     int nLdID: ld编号
* @param[in]     LIST_GTITILE& groupList : 保存信息的链表
* @param[in]     EC_GROUP_MAP&groupMap :  信息映射表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_____ReadLD_GTitleCfg(int nIedId,int nLdId,LIST_GTITILE& groupList,EC_GROUP_MAP&groupMap)
{
	char cError[255] = "";
	char cMapID[100] = "";
	ECU_GTITLE_TB* pCfg   = NULL;

	if( NULL == m_pDbmModel )
		return false;

	// 读取LD下的组标题配置
	m_pDbmModel->nxmodel_get_gtitile(nIedId,nLdId,groupList);
	int nListSize = groupList.size();
	if( nListSize <= 0 )
	{
		sprintf(cError,"读取IED=%d,LD=%d设备的组标题,nxmodel_get_gtitile()返回记录数为%d",nIedId,nLdId,nListSize);
		RecordErrorLog(cError);
		return false;
	}
	sprintf(cError,"读取IED=%d,LD=%d设备的组标题记录数为%d",nIedId,nLdId,nListSize);
	RecordTraceLog(cError);

	// 遍历记录并增加到map映射表
	LIST_GTITILE::iterator ListIte = groupList.begin();
	while( ListIte != groupList.end() )
	{
		pCfg = (ECU_GTITLE_TB *)(&(*ListIte));

		// 加入map 主键:"LD编号/组号",值:ECU_GTITLE_TB *
		_ZERO_MEM(cMapID,100);
		sprintf(cMapID,"%d/%d",nLdId,pCfg->n_gt_group);
		groupMap.insert( make_pair(cMapID,pCfg) );

#ifdef _DEBUG
		sprintf(cError,"IED(ID=%d,LD=%d)的映射表中增加group=%d、value=%x组标题记录成功",
			nIedId,nLdId,pCfg->n_gt_group,pCfg);
		RecordTraceLog(cError);
#endif
		pCfg = NULL;
		++ListIte;
	}
	sprintf(cError,"TOTAL:IED(ID=%d,LD=%d)的组标题映射表中增加%d条记录成功",
		nIedId,nLdId,groupMap.size());
	RecordTraceLog(cError);
	return true;
}

/**
* @brief         读取LD下录波模拟量通道
* @param[in]     int nIedID:ied编号
* @param[in]     int nLdID: ld编号
* @param[in]     LIST_OSC_AI& oscAiList : 保存信息的链表
* @param[in]     EC_OSC_AI_MAP &osc_aiMap :  信息映射表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_____ReadLD_OscAiCfg(int nIedID,int nLdID,LIST_OSC_AI& oscAiList,EC_OSC_AI_MAP &osc_aiMap)
{
	return true;
}

/**
* @brief         读取LD下录波开关量通道
* @param[in]     int nIedID:ied编号
* @param[in]     int nLdID: ld编号
* @param[in]     LIST_OSC_DI & oscDiList : 保存信息的链表
* @param[in]     EC_OSC_DI_MAP &osc_diMap :  信息映射表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_____ReadLD_OscDiCfg(int nIedID,int nLdID,LIST_OSC_DI & oscDiList,EC_OSC_DI_MAP &osc_diMap)
{
	return true;
}


/**
* @brief         释放全部变电站配置资源
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__FreeAllSubStationCfg()
{
	EC_SUBSTATION_MAP::iterator ite;
	EC_SUBSTATION * pStation = NULL;
	CAutoLockOnStack tempLock(&m_LockForSubStation);

	// 遍历释放资源
	ite = m_SubStationMap.begin();
	while( ite != m_SubStationMap.end() )
	{
		pStation = ite->second;

		if( NULL != pStation )
		{
			___FreeSubStationCfg( pStation );
			delete pStation;
			pStation = NULL;
		}

		++ite;
	}

	// 清空链表
	m_SubStationMap.clear();
	m_SubStationList.clear();
	RecordTraceLog("释放全部变电站资源完毕");

	return true;
}

/**
* @brief         释放指定变电站配置资源
* @param[in]     EC_SUBSTATION * pStationCfg：指定变电站配置指针
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::___FreeSubStationCfg( EC_SUBSTATION * pStationCfg )
{
	// 基本配置处理
	if( NULL != pStationCfg->pStation )
	{
		// 清空间隔链表
		pStationCfg->pStation->lst_bay.clear();

		// 释放变电站配置
		delete pStationCfg->pStation;
	}

	// 清空一次设备
	____FreePrimDevCfg(pStationCfg->PrimList,pStationCfg->PrimMap);

	// 清空二次设备
	____FreeIedCfg(pStationCfg->IedList,pStationCfg->IedMap);

	return true;
}

/**
* @brief         释放一次设备配置资源
* @param[in]     EC_PRIM_LIST &PrimList:一次设备信息链表
* @param[out]    EC_PRIM_MAP & PrimMap:一次设备映射表
* @return        bool :true-成功 false-失败
*/

bool CNXEcModelMgr::____FreePrimDevCfg(EC_PRIM_LIST &PrimList,EC_PRIM_MAP & PrimMap)
{
	EC_PRIM_LIST::iterator ite;
	const PRIMEQUIPMENT_TB * pPrimDev = NULL;

	ite = PrimList.begin();
	while( ite != PrimList.end() )
	{
		pPrimDev = (*ite);

		if( NULL != pPrimDev )
		{
			delete pPrimDev;
			pPrimDev = NULL;
		}
		++ite;
	}

	PrimList.clear();
	PrimMap.clear();
	return true;
}

/**
* @brief         释放二次设备配置资源
* @param[in]     EC_IED_LIST &IedList:二次设备链表
* @param[out]    EC_IED_MAP & IedMap:二次设备映射表
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::____FreeIedCfg(EC_IED_LIST &IedList,EC_IED_MAP & IedMap)
{
	EC_IED_LIST::iterator ite;
	EC_IED * pIedCfg = NULL;

	ite = IedList.begin();
	while( ite != IedList.end() )
	{
		pIedCfg = (*ite);
		if( NULL == pIedCfg )
		{
			++ ite;
			continue;;
		}

		// 删除IED基本配置 IED_TB*
		if( pIedCfg->pIed != NULL )
		{
			// 清除ld list
			pIedCfg->pIed->v_ld.clear();
			delete pIedCfg->pIed;
			pIedCfg->pIed = NULL;
		}

		// 清空IED下各映射map表
		pIedCfg->ldMap.clear();
		pIedCfg->sgMap.clear();
		pIedCfg->aiMap.clear();
		pIedCfg->diMap.clear();
		pIedCfg->softMap.clear();
		pIedCfg->eventMap.clear();
		pIedCfg->alramMap.clear();
		pIedCfg->faultMap.clear();
		pIedCfg->zoneMap.clear();
		pIedCfg->groupMap.clear();
		pIedCfg->osc_diMap.clear();
		pIedCfg->osc_aiMap.clear();

		// 删除IED下的各LD配置
		_____FreeIedLdCfg(pIedCfg->lDList);

		// 删除EC_IED
		delete pIedCfg;
		pIedCfg = NULL;
		++ite;
	}

	IedList.clear();
	IedMap.clear();
	return true;
}

/**
* @brief         释放二次设备LD配置资源
* @param[in]     EC_LD_LIST & ldList:ld配置链表
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::_____FreeIedLdCfg(EC_LD_LIST & ldList)
{
	EC_LD_LIST::iterator ite;
	EC_LD * pLdCfg = NULL;

	ite = ldList.begin();
	while( ite != ldList.end() )
	{
		pLdCfg = (*ite);
		if( NULL == pLdCfg )
		{
			++ite;
			continue;
		}

		// 清空ld下各信息链表
		pLdCfg->vGTitleList.clear();
		if( pLdCfg->pLdCfg != NULL )
		{
			pLdCfg->pLdCfg->v_alarm.clear();
			pLdCfg->pLdCfg->v_sg.clear();
			pLdCfg->pLdCfg->v_analog.clear();
			pLdCfg->pLdCfg->v_softstrap.clear();
			pLdCfg->pLdCfg->v_hardstrap.clear();
			pLdCfg->pLdCfg->v_sgzone.clear();
			pLdCfg->pLdCfg->v_event.clear();
			pLdCfg->pLdCfg->v_faulttag.clear();
			pLdCfg->pLdCfg->v_osc_di.clear();
			pLdCfg->pLdCfg->v_osc_ai.clear();
		}

		// 删除LD基本配置
		delete pLdCfg;
		pLdCfg = NULL;
		++ite;
	}

	ldList.clear();
	return true;
}

/**
* @brief         释放客户端配置资源
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__FreeClientCfg()
{
	ECU_CLIENT_TB * pClient = NULL;

	CAutoLockOnStack tempLock(&m_LockForClient);

	LIST_CLIENT::iterator ite = m_ClientList.begin();
	while( ite != m_ClientList.end())
	{
		pClient = (ECU_CLIENT_TB *)(&(*ite));

		if( NULL == pClient )
		{
			++ite;
			continue;
		}

		// 清除链表
		pClient->v_ChannelList.clear();
		pClient->v_NotOrderDevList.clear();
		pClient->v_MsgTypeOrderList.clear();

		pClient = NULL;
		++ite;
	}

	m_ClientList.clear();

	return true;
}

/**
* @brief         清空公用链表信息
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__ClearAllListAndMap()
{
	// 清空map表
	m_SubStationMap.clear();
	m_ProMap.clear();
	m_ComuStatusReasonMap.clear();
	m_TimeOutMap.clear();

	// 清空list
	m_ClientList.clear();
	m_ListenList.clear();
	m_SubStationList.clear();
	m_ProtocolList.clear();
	m_ComuStatusReasonList.clear();
	m_TimeOutList.clear();
	m_IedList.clear();

	return true;
}

/**
* @brief         释放基础模型访问库
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcModelMgr::__FreeDbm()
{
	if( m_pDbmModel == NULL )
		return true;

	// 销毁管理对象;
	m_NxLoadDbmLib.destroy_nxdbm_inst(m_pDbmModel);
	m_pDbmModel = NULL;

	// 卸载动态库
	m_NxLoadDbmLib.unload_nxdbm_lib();

	return true;
}

/**
* @brief         获取指定设备的状态信息
* @param[in]     EC_STATUS_INFO & statusInfo:状态信息结构中指定设备及状态类型
* @param[out]    EC_STATUS_INFO & statusInfo:状态信息结构中输出对应状态的值
* @return        true :true-成功 false-失败
*/
bool  CNXEcModelMgr::GetDevStatusInfo( IN OUT EC_STATUS_INFO & statusInfo )
{
	bool bOk = false;

	switch( statusInfo.eDevType )
	{
	case DEV_SECOND:
		bOk = _GetIedStatus( statusInfo );
		break;
	case DEV_STATION:
		break;
	case DEV_R_CLIENT:
		break;
	case DEV_PRIMARY:
		break;
	default:
		break;
	}

	return bOk ;
}

/**
* @brief         设置指定设备的状态信息
* @param[in]     EC_STATUS_INFO & statusInfo:状态信息结构
* @param[out]    无
* @return        true :true-成功 false-失败
*/
bool  CNXEcModelMgr::UpdateDevStatusInfo( IN EC_STATUS_INFO & statusInfo )
{
	bool bOk = false;

	switch( statusInfo.eDevType )
	{
	case DEV_SECOND:
		bOk = _SetIedStatus( statusInfo );
		break;
	case DEV_STATION:
		break;
	case DEV_R_CLIENT:
		break;
	case DEV_PRIMARY:
		break;
	default:
		break;
	}

	return bOk ;
}

/**
* @brief         获取指定IED设备的状态信息
* @param[in]     EC_STATUS_INFO & statusInfo:状态信息结构中指定设备及状态类型
* @param[out]    EC_STATUS_INFO & statusInfo:状态信息结构中输出对应状态的值
* @return        true :true-成功 false-失败
*/
bool  CNXEcModelMgr::_GetIedStatus( IN OUT EC_STATUS_INFO & statusInfo )
{
	bool bOk = false;
	CAutoLockOnStack IedLock(&m_LockForIed);

	LIST_IED::iterator ite = m_IedList.begin();

	while( ite != m_IedList.end() )
	{
		// 判断设备编号
		if( ite->n_obj_id != statusInfo.nDevID )
		{
			++ite;
			continue;
		}

		// 判断获取的信息类型
		if( statusInfo.eStatusType == EC_COMMU_STATUS )
		{
			statusInfo.nStatus = ite->n_cmmustat;
			statusInfo.nReason = ite->n_chgreason_obj;
			statusInfo.strTime = ite->str_cmmustattm;
			bOk = true;
		}
		else if( statusInfo.eStatusType == EC_RUN_STATUS )
		{
			statusInfo.nStatus = ite->e_opramode;
			statusInfo.strTime = ite->str_opramodetm;
			bOk = true;
		}
		break;
	}

	return bOk;
}

/**
* @brief         设置指定IED设备的状态信息
* @param[in]     EC_STATUS_INFO & statusInfo:状态信息结构
* @param[out]    无
* @return        true :true-成功 false-失败
*/
bool  CNXEcModelMgr::_SetIedStatus( IN EC_STATUS_INFO & statusInfo )
{
	bool bOk = false;
	CAutoLockOnStack IedLock(&m_LockForIed);

	LIST_IED::iterator ite = m_IedList.begin();

	while( ite != m_IedList.end() )
	{
		// 判断设备编号
		if( ite->n_obj_id != statusInfo.nDevID )
		{
			++ite;
			continue;
		}

		// 判断信息类型
		if( statusInfo.eStatusType == EC_COMMU_STATUS )
		{
			ite->n_cmmustat      = statusInfo.nStatus ;
			ite->n_chgreason_obj = statusInfo.nReason ;
			ite->str_cmmustattm  = statusInfo.strTime ;
			bOk = true;
		}
		else if( statusInfo.eStatusType == EC_RUN_STATUS )
		{
			ite->e_opramode     = (TOPRAMODE)statusInfo.nStatus ;
			ite->str_opramodetm = statusInfo.strTime ;
			bOk = true;
		}
		break;
	}

	return bOk;
}

/**
* @brief         获得指定变电站的IED状态列表
* @param[in]     UINT nStationID ：指定的站点编号，默认为全部IED
* @param[out]    IED_LIST & IedList：IED列表
* @return        true :true-成功 false-失败
*/
bool  CNXEcModelMgr::GetAllIedStatus( OUT LIST_IED & IedList,IN UINT nStationID/* = -1*/)
{
	CAutoLockOnStack IedLock(&m_LockForIed);

	if( nStationID == -1) // 全部
	{
		IedList.assign(m_IedList.begin(),m_IedList.end());
		return true;
	}

	LIST_IED::iterator ite = m_IedList.begin();

	while( ite != m_IedList.end() )
	{
		if( ite->n_station_obj == nStationID )
		{
			IedList.push_back(*ite);
		}

		++ite;
	}
	return true;
}

/******************************************************************************
*Function:		dbm_select_records	 
*Description:		查询记录 
*param[in]:		DB_OPER_PARAM*, 数据库操作参数结构指针
*param[out]:		CPlmRecordSet&, 数据记录集合
*param[in]:		bool,  是否强制使用distinct关键字
*param[out]:		char*, 错误信息
*Return:			int, 0:成功	1:失败
******************************************************************************/
int CNXEcModelMgr::dbm_select_records(DB_OPER_PARAM*  pParam, CPlmRecordSet& RcdSet,bool bDistinct,char* errinfo)
{
	return m_pDbmModel->dbm_select_records(pParam,RcdSet,bDistinct,errinfo);
}

/******************************************************************************
*Function:		dbm_update_record	 
*Description:		更新记录 
*param[in]:		DB_OPER_PARAM*, 数据库操作参数结构指针
*param[out]:		char*, 错误信息
*Return:			int, 0:成功	1:失败
******************************************************************************/
int CNXEcModelMgr::dbm_update_record(DB_OPER_PARAM*  pParam, char* errinfo)
{
	return m_pDbmModel->dbm_update_record(pParam,errinfo);
}

/******************************************************************************
*Function:		dmb_delete_record	 
*Description:		删除记录 
*param[in]:		DB_OPER_PARAM*, 数据库操作参数结构指针
*param[out]:		char*, 错误信息
*Return:			int, 0:成功	1:失败
******************************************************************************/
int CNXEcModelMgr::dmb_delete_record(DB_OPER_PARAM*  pParam, char *errinfo)
{
	return m_pDbmModel->dmb_delete_record(pParam,errinfo);
}

/******************************************************************************
*Function:		dbm_insert_record	 
*Description:		插入记录 
*param[in]:		DB_OPER_PARAM*, 数据库操作参数结构指针
*param[out]:		char*, 错误信息
*Return:			int, 0:成功	1:失败
******************************************************************************/
int	CNXEcModelMgr::dbm_insert_record(DB_OPER_PARAM*  pParam, char* errinfo)
{
	return  m_pDbmModel->dbm_insert_record(pParam,errinfo);
}