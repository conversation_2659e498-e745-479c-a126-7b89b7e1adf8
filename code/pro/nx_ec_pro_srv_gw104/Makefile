# 对外通信-国网104规约库  jjl 20130108

VER=1.0.26

# 头文件包含路径
INCLUDE_PATH = -I../../../../../nx_common/  \
               -I../../../../../platform_include/plm_common/  \
               -I../../../../../platform_include/plm_commun/  \
               -I../../../../../platform_include/plm_dbm/  \
               -I../../../../../thirdparty/tinyxml  \
               -I../../ec_common/  \
               -I../ec_pro_common/ \
               -I../ec_pro_common_iec60870/ \
               -I./  
               
# 平台名称(默认LINUX)
PLATNAME =__PLATFORM_OPEN_LINUX__

# linux平台下相关标识定义
ifeq ($(PLATNAME),__PLATFORM_OPEN_LINUX__)
    PLAT_LIBS = -rdynamic
    PLAT_FLAGS = 
endif 
         
# AIX平台定义
ifeq ($(PLATNAME),__PLATFORM_AIX_UNIX__)
    PLAT_LIBS =
    PLAT_FLAGS = -Wl,-G -fno-strict-aliasing 
endif 

# SUN平台定义
ifeq ($(PLATNAME),__PLATFORM_SUN_UNIX__)
    PLAT_LIBS = -lsocket
    PLAT_FLAGS = -B direct -z lazyload -z ignore
endif 

# HP平台定义
ifeq ($(PLATNAME),__PLATFORM_HP_UNIX__)
    PLAT_LIBS = 
    PLAT_FLAGS = -Wl,-G -fno-strict-aliasing -mlp64
endif 

# 发行版或调试版定义
_D =Y
ifeq ($(_D),Y)
     RUN_FLAGS = -g -D_DEBUG
     SYLIB_PATH  = -L../../../../../nx_lib/debug/
     ECLIB_PATH  = -L../../../../../nx_lib/debug/nx_ec/
     OUT_PATH    =../../../../../nx_bin/debug/nx_ec/
     OBJ_PATH    =./debug/
else
     RUN_FLAGS = -O2
     SYLIB_PATH  = -L../../../../../nx_lib/release/
     ECLIB_PATH  = -L../../../../../nx_lib/release/nx_ec/
     OUT_PATH    =../../../../../nx_bin/release/nx_ec/
     OBJ_PATH    =./release/
endif


# 依赖的运行库(sylib-平台公用文件 plmdb-访问数据库公用文件 )          
SY_LIBS = $(SYLIB_PATH) -lplm_db -lsylib -ltinyxml
EC_LIBS = $(ECLIB_PATH) -lec_pro_common_iec60870 -lec_pro_common -lec_common 

LIBS = -lpthread -ldl $(PLAT_LIBS) $(EC_LIBS) $(SY_LIBS)

# 编译标识
CFLAGS =$(RUN_FLAGS) -w -fpic -shared $(PLAT_FLAGS) -D$(PLATNAME) 

# 目标文件
OBJS   = $(OBJ_PATH)NXEcGW104SrvProtocol.o $(OBJ_PATH)ec_pro_srv_gw104_export.o $(OBJ_PATH)NXEc60870CvtFactory_GWS.o $(OBJ_PATH)NXEc60870CvtObj_GWS.o $(OBJ_PATH)NXEcProAsdu7_GWS.o $(OBJ_PATH)NXEcProAsdu10_GWS.o\
				 $(OBJ_PATH)NXEcProAsdu12_GWS.o $(OBJ_PATH)NXEcProAsdu13_GWS.o $(OBJ_PATH)NXEcProAsdu15_GWS.o $(OBJ_PATH)NXEcProAsdu16_GWS.o $(OBJ_PATH)NXEcProAsdu17_GWS.o $(OBJ_PATH)NXEcProAsdu103_GWS.o $(OBJ_PATH)NXEcProAsdu21_Direct_GWS.o \
				 $(OBJ_PATH)NXEcProAsdu1_GWS.o $(OBJ_PATH)NXEcProAsdu101_GWS.o $(OBJ_PATH)NXEcProAsdu42_GWS.o $(OBJ_PATH)NXEcProXmlHdl.o $(OBJ_PATH)CsgLogRecord.o $(OBJ_PATH)CsgLogRecordMngr.o $(OBJ_PATH)UnLibmngr_Gbk2Utf8.o $(OBJ_PATH)NXEcProAsdu109_GWS.o \
				 
# 编译器
CC = g++ 



libnx_ec_pro_srv_gw104.so-$(VER) : mkobjdir $(OBJS) mklibdir 
	$(CC) -o $(OUT_PATH)libnx_ec_pro_srv_gw104.so-$(VER) $(OBJS) $(CFLAGS) $(LIBS) $(INCLUDE_PATH)
	cp -f ./Gw103.ini $(OUT_PATH)

$(OBJ_PATH)NXEcGW104SrvProtocol.o : NXEcGW104SrvProtocol.cpp
	$(CC) -o $(OBJ_PATH)NXEcGW104SrvProtocol.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcGW104SrvProtocol.cpp

$(OBJ_PATH)ec_pro_srv_gw104_export.o : ec_pro_srv_gw104_export.cpp
	$(CC) -o $(OBJ_PATH)ec_pro_srv_gw104_export.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c ec_pro_srv_gw104_export.cpp

$(OBJ_PATH)NXEc60870CvtFactory_GWS.o : NXEc60870CvtFactory_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEc60870CvtFactory_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEc60870CvtFactory_GWS.cpp

$(OBJ_PATH)NXEc60870CvtObj_GWS.o : NXEc60870CvtObj_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEc60870CvtObj_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEc60870CvtObj_GWS.cpp

$(OBJ_PATH)NXEcProAsdu7_GWS.o : NXEcProAsdu7_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEcProAsdu7_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProAsdu7_GWS.cpp

$(OBJ_PATH)NXEcProAsdu10_GWS.o : NXEcProAsdu10_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEcProAsdu10_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProAsdu10_GWS.cpp

$(OBJ_PATH)NXEcProAsdu101_GWS.o : NXEcProAsdu101_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEcProAsdu101_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProAsdu101_GWS.cpp
	      
$(OBJ_PATH)NXEcProAsdu12_GWS.o : NXEcProAsdu12_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEcProAsdu12_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProAsdu12_GWS.cpp
	      
$(OBJ_PATH)NXEcProAsdu13_GWS.o : NXEcProAsdu13_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEcProAsdu13_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProAsdu13_GWS.cpp

$(OBJ_PATH)NXEcProAsdu15_GWS.o : NXEcProAsdu15_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEcProAsdu15_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProAsdu15_GWS.cpp	      
	      
$(OBJ_PATH)NXEcProAsdu16_GWS.o : NXEcProAsdu16_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEcProAsdu16_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProAsdu16_GWS.cpp
	      
$(OBJ_PATH)NXEcProAsdu17_GWS.o : NXEcProAsdu17_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEcProAsdu17_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProAsdu17_GWS.cpp

$(OBJ_PATH)NXEcProAsdu42_GWS.o : NXEcProAsdu42_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEcProAsdu42_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProAsdu42_GWS.cpp
	      
$(OBJ_PATH)NXEcProAsdu21_Direct_GWS.o : NXEcProAsdu21_Direct_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEcProAsdu21_Direct_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProAsdu21_Direct_GWS.cpp	     
	      
$(OBJ_PATH)NXEcProAsdu1_GWS.o : NXEcProAsdu1_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEcProAsdu1_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProAsdu1_GWS.cpp 

$(OBJ_PATH)NXEcProAsdu103_GWS.o : NXEcProAsdu103_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEcProAsdu103_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProAsdu103_GWS.cpp 

$(OBJ_PATH)NXEcProAsdu109_GWS.o : NXEcProAsdu109_GWS.cpp
	$(CC) -o $(OBJ_PATH)NXEcProAsdu109_GWS.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProAsdu109_GWS.cpp 

$(OBJ_PATH)NXEcProXmlHdl.o : NXEcProXmlHdl.cpp
	$(CC) -o $(OBJ_PATH)NXEcProXmlHdl.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcProXmlHdl.cpp 
	      
$(OBJ_PATH)UnLibmngr_Gbk2Utf8.o : ../../../../../nx_common/UnLibmngr_Gbk2Utf8.cpp 
	$(CC) -o $(OBJ_PATH)UnLibmngr_Gbk2Utf8.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c ../../../../../nx_common/UnLibmngr_Gbk2Utf8.cpp
	      
$(OBJ_PATH)CsgLogRecord.o : ../../../../../nx_common/CsgLogRecord.cpp 
	$(CC) -o $(OBJ_PATH)CsgLogRecord.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c ../../../../../nx_common/CsgLogRecord.cpp

$(OBJ_PATH)CsgLogRecordMngr.o : ../../../../../nx_common/CsgLogRecordMngr.cpp 
	$(CC) -o $(OBJ_PATH)CsgLogRecordMngr.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c ../../../../../nx_common/CsgLogRecordMngr.cpp	      
	      
mkobjdir:
	if [ -d $(OBJ_PATH) ]; then echo "$(OBJ_PATH) exists;";   else mkdir -p $(OBJ_PATH); fi

mklibdir:
	if [ -d $(OUT_PATH) ]; then echo "$(OUT_PATH) exists;";   else mkdir -p $(OUT_PATH); fi


.PHONY : all
all: main

.PHONY : install
install:
	@echo nothing done

.PHONY : print
print:
	@echo nothing done

.PHONY : tar
tar:
	@echo nothing done

.PHONY : clean

clean :
		-rm $(OUT_PATH)libnx_ec_pro_srv_gw104.so-$(VER) $(OBJS)
