/**********************************************************************
* NXEc60870CvtObj_GWS.h         author:Sl      date:03/12/2013            
*---------------------------------------------------------------------
*  note: 国网IEC103、104规约转换对象继承类头文件定义                                                              
*  
**********************************************************************/

#ifndef _H_NXEC60870CVTOBJ_GWS_H_ 
#define _H_NXEC60870CVTOBJ_GWS_H_

#include "NXEc60870CvtObj.h"
#include "NXEcProAsdu1_GWS.h"
#include "NXEcProAsdu7_GWS.h"
#include "NXEcProAsdu10_GWS.h"
#include "NXEcProAsdu12_GWS.h"
#include "NXEcProAsdu13_GWS.h"
#include "NXEcProAsdu15_GWS.h"
#include "NXEcProAsdu16_GWS.h"
#include "NXEcProAsdu17_GWS.h"
#include "NXEcProAsdu21_GWS.h"
#include "NXEcProAsdu42_GWS.h"
#include "NXEcProAsdu101_GWS.h"
#include "NXEcProAsdu103_GWS.h"
#include "NXEcProAsdu109_GWS.h"
#include "NXEcSrvProtocol.h"
/**
* @defgroup   CNXEc60870CvtObjGWS：60870规约转换对象继承类 
* @{
*/
 
/**
 * @brief      国网IEC103、104规约转换对象继承类头文件定义
 * <AUTHOR>
 * @date       03/12/2013
 *
 * example
 * @code*  
 *   
 *
 * @endcode
 */

class CNXEc60870CvtObjGWS:public CNXEc60870CvtObj
{
	///////////////////////////////////////////////////////////////构造、析构
public:
	
	/**
	* @brief         析构函数
	* @param[in]     无 
	* @param[out]    无
	* @return        无
	*/
	virtual ~CNXEc60870CvtObjGWS();

    /**
	* @brief         构造函数 
	* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
	* @param[out]    CLogRecord * pLogRecord:日志对象指针
	* @return        无
	*/
	CNXEc60870CvtObjGWS(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord,IN const SRV_PRO_START_PARAM * pParam);

	///////////////////////////////////////////////////////////////公用方法
public:	

	/**
	* @brief         转换规约信息到NX事件消息结构
	* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
	* @param[out]    NX_EVENT_MSG_LIST & lMsg :转换生成的事件消息列表
	* @return        int 0-成功 其它失败
	*/
	int ConvertProToEventMsg(IN  PRO_FRAME_BODY * pBody,OUT NX_EVENT_MSG_LIST & lMsg) ;

	/**
	* @brief         转换规约信息到NX通用消息结构
	* @param[in]     PRO_FRAME_BODY_LIST* pBodyList :规约信息体列表指针
	* @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
	* @param[out]    PRO_FRAME_BODY_LIST & lResult：保存生成的规约失败回应(服务端规约有效）
	* @return        >=0:成功 <0:失败
	*/
	int ConvertProToCommonMsg(IN PRO_FRAME_BODY_LIST* pBodyList,OUT NX_COMMON_MSG_LIST & lMsg,OUT PRO_FRAME_BODY_LIST & lResult);

	/**
	* @brief         直接从本地生成结果回应，如初始化配置信息;
	* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
	* @param[out]    PRO_FRAME_BODY_LIST & lResult：本地生成的结果帧体列表
	* @return        int 0-成功 其它失败
	*/
	int DirectResFromLocal(IN PRO_FRAME_BODY * pBody,OUT PRO_FRAME_BODY_LIST & lResult) ;

	/**
	* @brief         根据NX事件信息生成规约事件列表
	* @param[in]     NX_EVENT_MESSAGE * pMsg :事件信结构指针
	* @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
	* @return        int 0-成功 其它失败
	*/
	int ConvertEventMsgToPro(IN NX_EVENT_MESSAGE* pMsg,OUT PRO_FRAME_BODY_LIST & lBody) ;

	/**
	* @brief         根据NX通用信息及规约命令生成规约结果列表或根据通用消息生成规约命令
	* @param[in]     NX_COMMON_MESSAGE * pMsg :通用信息结构指针
	* @param[in][out]PRO_FRAME_BODY_LIST & lCmd:规约命令(服务端规约时为输入,客户端规约时为输出)
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表(服务端规约有效)
	* @return        int 0-成功 其它失败
	*/
	int ConvertCommonMsgToPro(IN NX_COMMON_MESSAGE * pMsg,IN OUT PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult);

	/**
	* @brief         设置规约属性
	* @param[in]     TNXEcProAsdu * pAsdu:asdu对象
	* @param[out]    无
	* @return        void
	*/
	void _SetProProperty(IN TNXEcProAsdu * pAsdu);

	/**
	* @brief         根据规约信息体内容获得ASDU13转换类型
	* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
	* @param[out]    无
	* @return        EC_PRO_CVT_TYPE:转换类型
	*/
	EC_PRO_CVT_TYPE _GetAsdu13CvtType(IN  PRO_FRAME_BODY *pBody);

	/**
	* @brief		判断设备的指定文件名(不含后缀)的cfg和dat文件是否存在.			
	* @param[in]    nAddr103:设备103地址
	* @param[in]    cFileName:文件名(不含后最)
	* @return		0-执行成功；其他-执行失败
	**/
	bool _WaveFilesExistInLocal(IN int nAddr103, IN char * cFileName);


	/**
	* @brief		根据IED做成该设备保存录波的完整路径信息.			
	* @param[in]    Addr103:设备103地址
	* @param[out]   cIedComtradePath:路径信息
	* @return		0-执行成功；其他-执行失败
	* @note 
	**/
	void __MakeIedComtradePath(IN int Addr103,IN char* cIedComtradePath);

	/**
	* @brief		获取指定目录下,具有相同文件名的所有后缀..			
	* @param[in]    char* cWaveFileName:录波文件名
	* @param[in]    vStr& vWaveFileExt:当查到本地磁盘有该文件存在,则将该文件的所有后缀名获取到队列中.
	* @return		0-执行成功；其他-执行失败
	* @note 
	**/
	bool __GetFileExts(IN char* cWaveFilePath,IN char* cWaveFileName,OUT vStr& vWaveFileExt);
	

	/** @brief              记录审计日志标识*/
	bool                    m_bRecord;

	/** @brief              计算机名称*/
	string                  m_strComputer;

	/** @brief              主站名称*/
	string                  m_strClientName;

	/** @brief              主站IP*/
	string                  m_strChannelIp;

	const SRV_PRO_START_PARAM * m_pParam;

	////////////////////////////////////////////////////////////////////////私有方法
private:
	/**
	* @brief 从ecpro.ini文件中,读取安全测试相关的配置.					
	* @note 
	**/
	void __ReadIni();
	/**
	* @brief 将状态信息写入数据库.					
	* @note 
	**/
	void __StatusToDb(IN string & strDecs);

	/** @brief              是否已经读取配置文档*/
	bool					m_bRead;

	////////////////////////////////////////////////////////////////////////私有成员
private:
};

/** @} */ //OVER


#endif // _H_NXEC60870CVTOBJ__GWS_H_