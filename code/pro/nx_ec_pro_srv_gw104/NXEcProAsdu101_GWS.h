/**********************************************************************
* NXEcProAsdu101_GWS.h         author:mjr      date:06/09/2021         
*---------------------------------------------------------------------
*  note: 国网ASDU103报文转换处理-简要故障报告                                                              
*  
**********************************************************************/

#ifndef _H_NXECPROASDU101_GWS_H_ 
#define _H_NXECPROASDU101_GWS_H_

#include "NXEcProAsdu101.h"


/**
* @defgroup   TNXEcProAsdu101GWS:ASDU103报文转换处理结点继承类
* @{
*/
 
/**
 * @brief      国网IEC103/104中ASDU101的转换处理基类
 * <AUTHOR>
 * @date       02/12/2020
 *
 * example
 * @code*  
 *   
 *
 * @endcode
 */
class TNXEcProAsdu101GWS:public TNXEcProAsdu101
{
	///////////////////////////////////////////////////////////////构造、析构
public:
	
	/**
	* @brief         析构函数
	* @param[in]     无 
	* @param[out]    无
	* @return        无
	*/
	~TNXEcProAsdu101GWS(){};

    /**
	* @brief         构造函数 
	* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
	* @param[out]    CLogRecord * pLogRecord:日志对象指针
	* @return        无
	*/
	TNXEcProAsdu101GWS(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord);

	///////////////////////////////////////////////////////////////公用方法
public:	

	/**
	* @brief		从召唤文件命令报文中,解析出文件名称(不包含路径)	
	* @param[in]    PRO_FRAME_BODY * pBody:命令报文指针
	* @param[out]   char * cFindFileName:文件名称  
	* @return		0-执行成功；其他-执行失败
	**/
	virtual int __GetFileNameFormProFrameBody(IN PRO_FRAME_BODY * pBody,OUT char * cFindFileName);

	/**
	* @brief         直接从本地生成结果回应，如初始化配置;---召唤通用文件列表
	* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
	* @param[out]    PRO_FRAME_BODY_LIST & lResult：本地生成的结果帧体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int DirectResFromLocal(IN PRO_FRAME_BODY * pBody,OUT PRO_FRAME_BODY_LIST & lResult);

	/**
	* @brief		根据指定的文件名,获取通用文件夹下的对应文件,组成列表返回			
	* @param[in]    ASDU_TIME AsduTime:时间范围
	* @param[in]    const char * cFindFileName:文件名
	* @param[in]    FILE_PROPERTY_INF_LIST & GeneralFileList:文件信息列表
	* @return		0-执行成功；其他-执行失败
	**/
	virtual int __QueryGeneralFilesList(IN ASDU_TIME AsduTime,IN const char * cFindFileName,OUT FILE_PROPERTY_INF_LIST & GeneralFileList);

	/**
	* @brief		根据指定的文件名,获取nxlog文件夹下的对应文件,组成列表返回
	* @param[in]    ASDU_TIME AsduTime:时间范围
	* @param[in]    const char * cFindFileName:文件名
	* @param[in]    FILE_PROPERTY_INF_LIST & GeneralFileList:文件信息列表
	* @return		0-执行成功；其他-执行失败
	**/
	virtual int __QueryGeneralFilesList_nxlog(IN ASDU_TIME AsduTime,IN const char * cFindFileName,OUT FILE_PROPERTY_INF_LIST & GeneralFileList);

	/**
	* @brief		根据指定的文件名,获取Setting文件夹下的对应文件,组成列表返回
	* @param[in]    ASDU_TIME AsduTime:时间范围
	* @param[in]    const char * cFindFileName:文件名
	* @param[in]    PRO_FRAME_BODY * pBody:报文体指针，用于获取addr地址
	* @param[in]    FILE_PROPERTY_INF_LIST & GeneralFileList:文件信息列表
	* @return		0-执行成功；其他-执行失败
	**/
	virtual int __QueryGeneralFilesList_SettingUp(IN ASDU_TIME AsduTime,IN const char * cFindFileName,IN PRO_FRAME_BODY * pBody,OUT FILE_PROPERTY_INF_LIST & GeneralFileList);

	//因新疆需求，新增配置，用于判断使用地区是否为新疆（是否使用新增的功能）使用地区 0-默认为一般地区（默认功能关闭） 1-特指新疆地区（功能开启）
	int m_using_area_101;

private:
	//asdu101读取Gw103文件
	void __ReadGw103Ini_101();

	//asdu101是否读取了Gwini
	bool bRunCheckPoint101;

	/**
	* @brief         字符串不区分大小写匹配
	* @param[in]     const char * s : 匹配字符串
	* @param[in]     const char * s2 :匹配字符串
	* @return        
	*/
	char* _strstr_nocase(const char * s, const char * s2);

};

/** @} */ //  TNXEcProAsdu101GWS OVER

#endif // _H_NXECPROASDU101_GWS_H_