/**********************************************************************
* NXEcProAsdu109_GWS.cpp         author:jjl      date:23/10/2013
*---------------------------------------------------------------------
*  note: ASDU109报文转换处理实现文件     --文件生成通知.
*
**********************************************************************/

#include "NXEcProAsdu109_GWS.h"


/**
* @brief         析构函数
* @param[in]     无
* @param[out]    无
* @return        无
*/
TNXEcProAsdu109GWS::~TNXEcProAsdu109GWS()
{
	m_pEventMsg = NULL;
}

/**
* @brief         构造函数
* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
* @param[out]    CLogRecord * pLogRecord:日志对象指针
* @return        无
*/
TNXEcProAsdu109GWS::TNXEcProAsdu109GWS(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord)
	:TNXEcProAsdu(pSeekIns,pLogRecord)
{
	m_pEventMsg = NULL;
	// 设置类名称
	_SetLogClassName("TNXEcProAsdu109GWS");
}


/**
* @brief         根据NX事件信息生成规约事件列表
* @param[in]     NX_EVENT_MESSAGE * pMsg :事件信息结构指针
* @param[out]    PRO_FRAME_BODY_LIST & lBody :规约信息体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu109GWS::ConvertEventMsgToPro(IN NX_EVENT_MESSAGE* pMsg, OUT PRO_FRAME_BODY_LIST & lBody)
{
	char cError[255] = "";
	int nRet = 0;

	m_pEventMsg = pMsg;

	switch(pMsg->n_msg_type)
	{
	case NX_IED_EVENT_FILE_REPORT:  // 文件生成事件
		nRet = CvtEventFileReport(lBody);
		break;
	default:
		sprintf(cError,"ConvertEventMsgToPro()中暂不支持n_msg_type=%d的消息处理",pMsg->n_msg_type);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu109GWS");
		nRet = EC_PRO_CVT_NOSUPPORT;
		break;
	}

	m_pEventMsg = NULL;
	return nRet;
}

/**
* @brief         转换NX文件生成事件格式为规约事件格式
* @param[out]    PRO_FRAME_BODY_LIST & lBody :规约信息体列表
* @return        int 0-成功 其它失败
* @note          处理NX_IED_EVENT_FILE_REPORT类型事件，使用n_event_obj作为IED编号
*/
int TNXEcProAsdu109GWS::CvtEventFileReport(OUT PRO_FRAME_BODY_LIST & lBody)
{
	char cError[255] = "";

	// 获得变电站地址
	const SUBSTATION_TB * pStation = m_pModelSeek->GetSubStationBasicCfg();
	if (pStation == NULL)
	{
		RcdErrLogWithParentClass("CvtEventFileReport:获取变电站基本配置失败.","TNXEcProAsdu109GWS");
		return EC_PRO_CVT_FAIL;
	}

	// 获得设备信息 - 使用n_event_obj字段作为IED编号
	const IED_TB * pIed = m_pModelSeek->GetIedBasicCfgByID(m_pEventMsg->n_event_obj);
	if (pIed == NULL)
	{
		sprintf(cError,"CvtEventFileReport:获取设备[%d]基本配置失败.",m_pEventMsg->n_event_obj);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu109GWS");
		return EC_PRO_CVT_FAIL;
	}

	// 构建ASDU109信息结构
	ASDU109_INFO Asdu109Info;

	// 地址信息
	Asdu109Info.Addr.nSubstationAdd = pStation->n_outaddr103;
	Asdu109Info.Addr.nAddr = pIed->n_outaddr103;
	Asdu109Info.Addr.nCpu = 0; // 默认CPU号
	Asdu109Info.Addr.nZone = 0; // 默认定值区号

	// 信息体 - ASDU109固定值
	Asdu109Info.InfoObj.nFun = 0xA2; // 功能类型：文件生成通知
	Asdu109Info.InfoObj.nInf = 0x00; // 信息序号
	Asdu109Info.InfoObj.nDpi = 0;    // DPI值

	// 文件类型：1-定值文件
	Asdu109Info.nFileType = 1;

	// 时间信息
	Asdu109Info.InfoTime.nInfoHappenUtc = m_pEventMsg->n_send_utctm;
	Asdu109Info.InfoTime.nInfoHappenMs = 0;
	Asdu109Info.InfoTime.nInfoRcvUtc = m_pEventMsg->n_send_utctm;
	Asdu109Info.InfoTime.nInfoRcvMs = 0;

	// 文件名处理 - 从list_subfields中获取文件信息
	string strFileName = "";

	// 检查list_subfields是否有数据
	if (m_pEventMsg->list_subfields.size() <= 0)
	{
		sprintf(cError,"CvtEventFileReport:NX事件消息的list_subfields为空,IED编号:%d",m_pEventMsg->n_event_obj);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu109GWS");
		return EC_PRO_CVT_FAIL;
	}

	// 遍历list_subfields查找文件名信息
	NX_EVENT_MSG_SUBFILED_LIST::iterator ite = m_pEventMsg->list_subfields.begin();
	while (ite != m_pEventMsg->list_subfields.end())
	{
		if (strlen(ite->c_field_name) > 0)
			{
				strFileName = ite->c_field_name;
				sprintf(cError,"CvtEventFileReport:使用c_field_name作为文件名:%s",strFileName.c_str());
				RcdTrcLogWithParentClass(cError,"TNXEcProAsdu109GWS");
				break;
			}
		++ite;
	}

	// 最终验证文件名是否获取成功
	if (strFileName.empty())
	{
		sprintf(cError,"CvtEventFileReport:无法从NX报文list_subfields获取文件名,IED编号:%d,字段数量:%d",
			m_pEventMsg->n_event_obj, m_pEventMsg->list_subfields.size());
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu109GWS");
		return EC_PRO_CVT_FAIL;
	}

	Asdu109Info.strWavFileName = strFileName;

	// 格式化ASDU109报文体
	if (FormatAsdu109Body(Asdu109Info, lBody) < 0)
	{
		RcdErrLogWithParentClass("CvtEventFileReport:格式化ASDU109报文体失败.","TNXEcProAsdu109GWS");
		return EC_PRO_CVT_FAIL;
	}

	if (lBody.size() <= 0)
	{
		sprintf(cError,"CvtEventFileReport():转换文件生成事件消息失败,IED编号:%d",m_pEventMsg->n_event_obj);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu109GWS");
		return EC_PRO_CVT_FAIL;
	}
	else
	{
		sprintf(cError,"CvtEventFileReport():转换文件生成事件消息为%d条ASDU109,IED编号:%d,文件名:%s",
			lBody.size(), m_pEventMsg->n_event_obj, Asdu109Info.strWavFileName.c_str());
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu109GWS");
	}

	return 0;
}

/**
* @brief         根据ASDU109信息结构格式化ASDU109报文体
* @param[in]     ASDU109_INFO &Asdu109Info: ASDU109信息结构体
* @param[out]    PRO_FRAME_BODY_LIST & lBody :规约信息体列表
* @param[in]     int nReserve:备用参数（默认0）
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu109GWS::FormatAsdu109Body(IN ASDU109_INFO &Asdu109Info, OUT PRO_FRAME_BODY_LIST & lBody, IN int nReserve)
{
	PRO_FRAME_BODY FrameBody;
	char cError[255] = "";

	// 固定部分
	FrameBody.nType       = 0x6D;  // 类型标识：6DH
	FrameBody.nVsq        = 0x81;  // 可变结构限定词：81H
	FrameBody.nCot        = 0x01;  // 传送原因：01H (自发)
	FrameBody.nSubstationAdd = Asdu109Info.Addr.nSubstationAdd;
	FrameBody.nAddr       = Asdu109Info.Addr.nAddr;
	FrameBody.nCpu        = Asdu109Info.Addr.nCpu;
	FrameBody.nZone       = Asdu109Info.Addr.nZone;
	FrameBody.nFun        = Asdu109Info.InfoObj.nFun;
	FrameBody.nInf        = Asdu109Info.InfoObj.nInf;

	// 可变部分
	// 文件类型 (1字节)
	FrameBody.vVarData.push_back(Asdu109Info.nFileType);

	// 文件生成时间 (CP56Time2a格式，7字节)
	CTimeConvert HappenTmCvt(Asdu109Info.InfoTime.nInfoHappenUtc, Asdu109Info.InfoTime.nInfoHappenMs);
	string strHappenTm;
	HappenTmCvt.GetCP56TIMe(strHappenTm);
	FrameBody.vVarData.insert(FrameBody.vVarData.end(), strHappenTm.begin(), strHappenTm.end());

	// 文件名 (256字节，带后缀，未使用字节补0)
	string strFileName = Asdu109Info.strWavFileName;
	if (strFileName.length() > 255)
	{
		sprintf(cError,"FormatAsdu109Body:文件名长度[%d]超过255字节限制,将被截断", strFileName.length());
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu109GWS");
		strFileName = strFileName.substr(0, 255);
	}

	// 使用resize方法预分配256字节空间
	size_t nCurrentSize = FrameBody.vVarData.size();
	FrameBody.vVarData.resize(nCurrentSize + 256, 0);
	if (!strFileName.empty())
	{
		memcpy(&(FrameBody.vVarData[nCurrentSize]), strFileName.c_str(), strFileName.length());
	}

	// 附加信息 (40字节，未使用字节补0)
	string strReserve = Asdu109Info.strReserve;
	if (strReserve.length() > 39)
	{
		sprintf(cError,"FormatAsdu109Body:附加信息长度[%d]超过39字节限制,将被截断", strReserve.length());
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu109GWS");
		strReserve = strReserve.substr(0, 39);
	}

	// 使用resize方法预分配40字节空间
	nCurrentSize = FrameBody.vVarData.size();
	FrameBody.vVarData.resize(nCurrentSize + 40, 0);
	if (!strReserve.empty())
	{
		memcpy(&(FrameBody.vVarData[nCurrentSize]), strReserve.c_str(), strReserve.length());
	}

	// 添加到输出列表
	lBody.push_back(FrameBody);

	sprintf(cError,"FormatAsdu109Body:成功格式化ASDU109报文,addr=%d cpu=%d 文件名:%s 文件类型:%d 报文长度:%d",
		Asdu109Info.Addr.nAddr, Asdu109Info.Addr.nCpu, strFileName.c_str(),
		Asdu109Info.nFileType, FrameBody.vVarData.size());
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu109GWS");

	// 扩展预留
	FrameBody.vVarData.clear();
	return 0;
}
