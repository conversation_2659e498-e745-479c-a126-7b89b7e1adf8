/**********************************************************************
* NXEcProAsdu109_GWS.h         author:jjl      date:23/10/2013
*---------------------------------------------------------------------
*  note: ASDU109报文转换处理头文件
*
**********************************************************************/

#ifndef _H_NXECPROASDU109GWS_H_
#define _H_NXECPROASDU109GWS_H_

#include "NXEcProAsdu.h"

/**
* @defgroup   TNXEcProAsdu109GWS:ASDU109报文转换处理结点类
* @{
*/

/** @brief         ASDU109信息*/
typedef struct _ASDU109_INFO
{
	/** @brief         地址信息*/
	ASDU_ADDR         Addr;
	/** @brief         信息体*/
	ASDU_INFO_OBJ     InfoObj;
	/** @brief         文件类型（1-定值文件）*/
	u_int8            nFileType;
	/** @brief         时间*/
	ASDU_TIME         InfoTime;
	/** @brief         文件名*/
	string            strWavFileName;
	/** @brief         备用字符*/
	string            strReserve;

	_ASDU109_INFO()
	{
		nFileType = 1; // 默认为定值文件
	}
}ASDU109_INFO;

/**
 * @brief      ASDU109报文转换处理基类
 * <AUTHOR>
 * @date       23/10/2013
 *
 * example
 * @code*
 *
 *
 * @endcode
 */

class TNXEcProAsdu109GWS:public TNXEcProAsdu
{
	///////////////////////////////////////////////////////////////构造、析构
public:

	/**
	* @brief         析构函数
	* @param[in]     无
	* @param[out]    无
	* @return        无
	*/
	virtual ~TNXEcProAsdu109GWS();

    /**
	* @brief         构造函数
	* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
	* @param[out]    CLogRecord * pLogRecord:日志对象指针
	* @return        无
	*/
	TNXEcProAsdu109GWS(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord);

	///////////////////////////////////////////////////////////////公用方法
public:

	/**
	* @brief         根据NX事件信息生成规约事件列表
	* @param[in]     NX_EVENT_MESSAGE * pMsg :事件信息结构指针
	* @param[out]    PRO_FRAME_BODY_LIST & lBody :规约信息体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int ConvertEventMsgToPro(IN NX_EVENT_MESSAGE* pMsg, OUT PRO_FRAME_BODY_LIST & lBody);

	/**
	* @brief         根据ASDU109信息结构格式化ASDU109报文体
	* @param[in]     ASDU109_INFO &Asdu109Info: ASDU109信息结构体
	* @param[out]    PRO_FRAME_BODY_LIST & lBody :规约信息体列表
	* @param[in]     int nReserve:备用参数（默认0）
	* @return        int 0-成功 其它失败
	*/
	virtual int FormatAsdu109Body(IN ASDU109_INFO &Asdu109Info, OUT PRO_FRAME_BODY_LIST & lBody, IN int nReserve = 0);

	/**
	* @brief         转换NX文件生成事件格式为规约事件格式
	* @param[out]    PRO_FRAME_BODY_LIST & lBody :规约信息体列表
	* @return        int 0-成功 其它失败
	* @note          处理NX_IED_EVENT_FILE_REPORT类型事件，使用n_event_obj作为IED编号
	*/
	virtual int CvtEventFileReport(OUT PRO_FRAME_BODY_LIST & lBody);

	////////////////////////////////////////////////////////////////////////保护方法
protected:

	////////////////////////////////////////////////////////////////////////私有方法
private:

	////////////////////////////////////////////////////////////////////////保护成员
protected:

	/** @brief         事件消息指针*/
	NX_EVENT_MESSAGE * m_pEventMsg;

	////////////////////////////////////////////////////////////////////////私有成员
private:
};


/** @} */ //OVER


#endif  // _H_NXECPROASDU109GWS_H_