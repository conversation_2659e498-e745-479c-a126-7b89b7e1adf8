#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/logger.hpp"
#include <iostream>
#include <map>
#include <string>
using namespace zexuan;
/**
 * 简化的数据处理插件 - 使用新的PluginBase类
 */
class SummaryPlugin : public zexuan::plugin::PluginBase {
public:
    SummaryPlugin(std::shared_ptr<zexuan::base::Mediator> mediator, int pluginId, const std::string& description)
        : PluginBase(mediator, pluginId, description) {
    }

    bool initialize() override {
        auto logger = Logger::getFileLogger("plugin/summary_plugin");
        logger->info("Plugin {} initializing...", getPluginId());
        // 重置统计信息
        dataProcessingStats_.clear();
        return true;
    }

    void shutdown() override {
        auto logger = Logger::getFileLogger("plugin/summary_plugin");
        logger->info("Plugin {} shutting down...", getPluginId());
    }

protected:
    void processMessage(const zexuan::base::Message& message) override {
        auto logger = Logger::getFileLogger("plugin/summary_plugin");
        logger->debug("SummaryPlugin {} received message: TYP={}, COT={}, Source={}, Target={}",
                     getPluginId(),
                     static_cast<int>(message.getTyp()),
                     static_cast<int>(message.getCot()),
                     static_cast<int>(message.getSource()),
                     static_cast<int>(message.getTarget()));

        // 处理数据处理请求
        if (message.getCot() == 0x06) { // Activation
            processDataRequest(message);
        } else if (message.getCot() == 0x01) { // Confirmation
            logger->info("{}",getPluginDescription());
        }
    }

private:
    // === 成员变量 ===
    std::map<std::string, double> dataProcessingStats_;

    // === 核心功能方法 ===
    void processDataRequest(const zexuan::base::Message& message) {
        auto logger = Logger::getFileLogger("plugin/summary_plugin");
        logger->info("SummaryPlugin {} getdata {}", getPluginId(),message.getTextContent());

    }
};

// === 插件导出函数 ===
extern "C" {
    zexuan::plugin::PluginBase* create_plugin(void* mediator, int pluginId, const char* description) {
        auto med = static_cast<std::shared_ptr<zexuan::base::Mediator>*>(mediator);
        return new SummaryPlugin(*med, pluginId, std::string(description));
    }

    void destroy_plugin(zexuan::plugin::PluginBase* plugin) {
        delete plugin;
    }

    const char* get_plugin_info() {
        return "Summary Plugin v1.0.0 - Using new PluginBase architecture";
    }
}
