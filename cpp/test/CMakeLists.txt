# 配置示例
add_executable(filename_test filename_test.cpp)
target_link_libraries(filename_test
    PRIVATE
        core
        comic_plugin
        gtest::gtest
)

# ComicPlugin 单元测试
add_executable(comic_plugin_test test_comic_plugin.cpp)
target_link_libraries(comic_plugin_test
    PRIVATE
        core
        gtest::gtest
        gtest::gtest_main
        gmock::gmock
        gmock::gmock_main
        pthread
)

# 包含头文件目录
target_include_directories(comic_plugin_test PRIVATE
    ${CMAKE_SOURCE_DIR}/cpp/core/include
    ${CMAKE_SOURCE_DIR}/cpp/plugins/comic_plugin/include
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# 设置编译选项
target_compile_options(comic_plugin_test PRIVATE
    -Wall
    -Wextra
    -g
    -O0
    -std=c++20
)

# 设置所有测试的输出目录
set_target_properties(
    filename_test
    comic_plugin_test
    PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/bin/test"
)

# 启用测试
enable_testing()

# 添加测试
add_test(NAME ComicPluginTest COMMAND comic_plugin_test)