#pragma once

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <string>
#include <vector>
#include <filesystem>
#include <fstream>
#include <thread>
#include <chrono>
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/message.hpp"

namespace fs = std::filesystem;

// Mock Mediator 类
class MockMediator : public zexuan::base::Mediator {
public:
    MOCK_METHOD(bool, initialize, (), (override));
    MOCK_METHOD(bool, registerObserver, (int observerId, zexuan::base::Observer* observer, std::string& errorMsg), (override));
    MOCK_METHOD(bool, unregisterObserver, (int observerId, std::string& errorMsg), (override));
    MOCK_METHOD(int, sendMessage, (const zexuan::base::Message& message, std::string& description), (override));
};

// 使用真实的 Message 类

// 直接使用真实的 ThreadPool

// Mock FileUtils 类
class MockFileUtils {
public:
    static std::string generateFileNameFromCreationTime(const std::string& filePath) {
        // 模拟生成基于时间的文件名
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);
        
        char timeStr[20];
        std::strftime(timeStr, sizeof(timeStr), "%Y%m%d%H%M%S", &tm);
        
        // 模拟UUID
        std::string uuid = "c87398b4-0e7e-4ef5-9d8f-7e2dbde914cc";
        
        // 获取文件扩展名
        fs::path path(filePath);
        std::string extension = path.extension().string();
        
        return std::string(timeStr) + "_" + uuid + extension;
    }
};
// 测试工具类
class ComicPluginTestUtils {
public:
    // 创建测试目录结构
    static void createTestDirectory(const std::string& basePath, const std::vector<std::string>& fileNames) {
        try {
            fs::create_directories(basePath);
            
            for (const auto& fileName : fileNames) {
                std::string filePath = basePath + "/" + fileName;
                std::ofstream file(filePath);
                if (file.is_open()) {
                    file << "Test content for " << fileName << std::endl;
                    file.close();
                }
            }
            
            std::cout << "Created test directory: " << basePath << " with " << fileNames.size() << " files" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "Failed to create test directory: " << e.what() << std::endl;
        }
    }

    // 清理测试目录
    static void cleanupTestDirectory(const std::string& basePath) {
        try {
            if (fs::exists(basePath)) {
                fs::remove_all(basePath);
                std::cout << "Cleaned up test directory: " << basePath << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "Failed to cleanup test directory: " << e.what() << std::endl;
        }
    }

    // 验证文件是否存在
    static bool fileExists(const std::string& filePath) {
        return fs::exists(filePath) && fs::is_regular_file(filePath);
    }

    // 获取目录中的文件列表
    static std::vector<std::string> getFilesInDirectory(const std::string& directoryPath) {
        std::vector<std::string> files;
        try {
            if (fs::exists(directoryPath) && fs::is_directory(directoryPath)) {
                for (const auto& entry : fs::directory_iterator(directoryPath)) {
                    if (entry.is_regular_file()) {
                        files.push_back(entry.path().filename().string());
                    }
                }
            }
        } catch (const std::exception& e) {
            std::cerr << "Error reading directory: " << e.what() << std::endl;
        }
        return files;
    }

    // 验证文件名格式是否正确
    static bool isValidRenamedFileName(const std::string& fileName) {
        // 检查格式：YYYYMMDDHHMMSS_uuid.ext
        if (fileName.length() < 20) return false;
        
        // 检查时间戳部分（前14个字符应该是数字）
        for (int i = 0; i < 14; ++i) {
            if (!std::isdigit(fileName[i])) return false;
        }
        
        // 检查下划线
        if (fileName[14] != '_') return false;
        
        // 检查是否包含UUID格式的字符串
        size_t dotPos = fileName.find_last_of('.');
        if (dotPos == std::string::npos) return false;
        
        std::string uuidPart = fileName.substr(15, dotPos - 15);
        return uuidPart.length() >= 36; // UUID最少36个字符
    }
};
