#pragma once

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <string>
#include <vector>
#include <filesystem>
#include <fstream>
#include <thread>
#include <chrono>
#include <future>
#include <mutex>
#include <atomic>
#include "zexuan/thread_pool.hpp"

// 前向声明
namespace zexuan {
    namespace base {
        class Mediator;
        class Message;
        class Observer;
    }
    namespace utils {
        class FileUtils;
    }
    class ThreadPool;
    namespace plugin {
        class PluginBase;
    }
}

namespace fs = std::filesystem;

// Mock Mediator 类
class MockMediator : public zexuan::base::Mediator {
public:
    MOCK_METHOD(bool, initialize, (), (override));
    MOCK_METHOD(bool, registerObserver, (int observerId, zexuan::base::Observer* observer, std::string& errorMsg), (override));
    MOCK_METHOD(bool, unregisterObserver, (int observerId, std::string& errorMsg), (override));
    MOCK_METHOD(int, sendMessage, (const zexuan::base::Message& message, std::string& description), (override));
};

// Mock Message 类
class MockMessage {
public:
    MockMessage(uint8_t typ = 0, uint8_t vsq = 0, uint8_t cot = 0, uint8_t source = 0,
                uint8_t fun = 0, uint8_t inf = 0, const std::string& textContent = "")
        : typ_(typ), vsq_(vsq), cot_(cot), source_(source), target_(0),
          fun_(fun), inf_(inf), textContent_(textContent) {}

    uint8_t getTyp() const { return typ_; }
    uint8_t getVsq() const { return vsq_; }
    uint8_t getCot() const { return cot_; }
    uint8_t getSource() const { return source_; }
    uint8_t getTarget() const { return target_; }
    uint8_t getFun() const { return fun_; }
    uint8_t getInf() const { return inf_; }
    std::string getTextContent() const { return textContent_; }

    void setTyp(uint8_t typ) { typ_ = typ; }
    void setVsq(uint8_t vsq) { vsq_ = vsq; }
    void setCot(uint8_t cot) { cot_ = cot; }
    void setSource(uint8_t source) { source_ = source; }
    void setTarget(uint8_t target) { target_ = target; }
    void setFun(uint8_t fun) { fun_ = fun; }
    void setInf(uint8_t inf) { inf_ = inf; }
    void setTextContent(const std::string& content) { textContent_ = content; }

private:
    uint8_t typ_, vsq_, cot_, source_, target_, fun_, inf_;
    std::string textContent_;
};

// 直接使用真实的 ThreadPool

// Mock FileUtils 类
class MockFileUtils {
public:
    static std::string generateFileNameFromCreationTime(const std::string& filePath) {
        // 模拟生成基于时间的文件名
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);
        
        char timeStr[20];
        std::strftime(timeStr, sizeof(timeStr), "%Y%m%d%H%M%S", &tm);
        
        // 模拟UUID
        std::string uuid = "c87398b4-0e7e-4ef5-9d8f-7e2dbde914cc";
        
        // 获取文件扩展名
        fs::path path(filePath);
        std::string extension = path.extension().string();
        
        return std::string(timeStr) + "_" + uuid + extension;
    }
};

// 测试用的 ComicPlugin 类（继承自实际的 ComicPlugin）
class TestableComicPlugin {
public:
    TestableComicPlugin(std::shared_ptr<MockMediator> mediator, int pluginId, const std::string& description)
        : mediator_(mediator), pluginId_(pluginId), description_(description),
          threadPool_(std::make_unique<zexuan::ThreadPool>(8)),
          processedFiles_(0), successfulRenames_(0), failedRenames_(0) {}

    // 公开的测试接口
    bool initialize() {
        std::cout << "ComicPlugin " << pluginId_ << " initializing..." << std::endl;
        return true;
    }

    void shutdown() {
        std::cout << "ComicPlugin " << pluginId_ << " shutting down..." << std::endl;
    }

    int getPluginId() const { return pluginId_; }

    // 公开核心方法以便测试
    void processMessage(const MockMessage& message) {
        std::cout << "ComicPlugin " << pluginId_
                  << " received message: TYP=" << static_cast<int>(message.getTyp())
                  << ", COT=" << static_cast<int>(message.getCot())
                  << ", Source=" << static_cast<int>(message.getSource())
                  << ", Target=" << static_cast<int>(message.getTarget()) << std::endl;

        if (message.getFun() == 0x01 && message.getCot() == 0x06) {
            handleFileRenameMessage(message);
        } else {
            std::cout << "ComicPlugin " << pluginId_ << " received unknown message type" << std::endl;
        }
    }

    void handleFileRenameMessage(const MockMessage& message) {
        std::string directoryPath = message.getTextContent();

        if (directoryPath.empty()) {
            std::cerr << "ComicPlugin: No directory path provided in message" << std::endl;
            return;
        }

        std::cout << "ComicPlugin " << pluginId_ << " processing directory: " << directoryPath << std::endl;
        processDirectoryMultiThreaded(directoryPath);
    }

    bool processFolder(const std::string& folderPath) {
        std::cout << "ComicPlugin " << pluginId_ << " processing folder: " << folderPath << std::endl;

        if (!fs::exists(folderPath) || !fs::is_directory(folderPath)) {
            std::cout << "ComicPlugin: Error - Invalid folder path: " << folderPath << std::endl;
            return false;
        }

        processedFiles_ = 0;
        successfulRenames_ = 0;
        failedRenames_ = 0;

        try {
            std::vector<fs::path> files;
            for (const auto& entry : fs::directory_iterator(folderPath)) {
                if (entry.is_regular_file()) {
                    files.push_back(entry.path());
                }
            }

            std::cout << "ComicPlugin: Found " << files.size() << " files to process" << std::endl;

            for (const auto& filePath : files) {
                if (renameFile(filePath)) {
                    successfulRenames_++;
                } else {
                    failedRenames_++;
                }
                processedFiles_++;
            }

            std::cout << "ComicPlugin: Successfully processed " << successfulRenames_.load() << "/" << files.size() << " files" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "ComicPlugin: Error during processing: " << e.what() << std::endl;
            return false;
        }
    }

    bool renameFile(const fs::path& filePath) {
        try {
            std::string newName = generateNewFileName(filePath.string());
            fs::path newPath = filePath.parent_path() / newName;

            int counter = 1;
            while (fs::exists(newPath)) {
                std::string baseName = newName.substr(0, newName.find_last_of('.'));
                std::string extension = newName.substr(newName.find_last_of('.'));
                newPath = filePath.parent_path() / (baseName + "_" + std::to_string(counter) + extension);
                counter++;
            }

            fs::rename(filePath, newPath);
            std::cout << "ComicPlugin: Renamed " << filePath.filename().string()
                      << " -> " << newPath.filename().string() << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "ComicPlugin: Failed to rename " << filePath.filename().string()
                      << ": " << e.what() << std::endl;
            return false;
        }
    }

    std::string generateNewFileName(const std::string& filePath) {
        return MockFileUtils::generateFileNameFromCreationTime(filePath);
    }

    void processDirectoryMultiThreaded(const std::string& directoryPath) {
        if (!fs::exists(directoryPath) || !fs::is_directory(directoryPath)) {
            std::cerr << "ComicPlugin: Error - Invalid directory path: " << directoryPath << std::endl;
            return;
        }

        processedFiles_ = 0;
        successfulRenames_ = 0;
        failedRenames_ = 0;

        std::vector<fs::path> files;
        try {
            for (const auto& entry : fs::directory_iterator(directoryPath)) {
                if (entry.is_regular_file()) {
                    files.push_back(entry.path());
                }
            }
        } catch (const fs::filesystem_error& e) {
            std::cerr << "ComicPlugin: Error reading directory: " << e.what() << std::endl;
            return;
        }

        if (files.empty()) {
            std::cout << "ComicPlugin: No files found in directory: " << directoryPath << std::endl;
            return;
        }

        std::cout << "ComicPlugin: Found " << files.size() << " files to process using "
                  << threadPool_->thrCount() << " threads" << std::endl;

        std::vector<std::future<void>> futures;
        futures.reserve(files.size());

        for (const auto& filePath : files) {
            auto future = threadPool_->commit([this, filePath]() {
                renameFileThreadSafe(filePath);
            });
            futures.push_back(std::move(future));
        }

        for (auto& future : futures) {
            try {
                future.get();
            } catch (const std::exception& e) {
                std::cerr << "ComicPlugin: Task execution error: " << e.what() << std::endl;
            }
        }

        std::cout << "ComicPlugin: Processing completed!" << std::endl;
        std::cout << "  Total files: " << files.size() << std::endl;
        std::cout << "  Processed: " << processedFiles_.load() << std::endl;
        std::cout << "  Successful renames: " << successfulRenames_.load() << std::endl;
        std::cout << "  Failed renames: " << failedRenames_.load() << std::endl;
    }

    void renameFileThreadSafe(const fs::path& filePath) {
        try {
            std::string newFileName = generateNewFileName(filePath.string());
            fs::path newPath = filePath.parent_path() / newFileName;

            int counter = 1;
            while (fs::exists(newPath)) {
                std::string baseName = newFileName.substr(0, newFileName.find_last_of('.'));
                std::string ext = newFileName.substr(newFileName.find_last_of('.'));
                newPath = filePath.parent_path() / (baseName + "_" + std::to_string(counter) + ext);
                counter++;
            }

            fs::rename(filePath, newPath);

            {
                std::lock_guard<std::mutex> lock(resultMutex_);
                successfulRenames_++;
                std::cout << "ComicPlugin [Thread " << std::this_thread::get_id() << "]: "
                          << "Renamed: " << filePath.filename().string()
                          << " -> " << newPath.filename().string() << std::endl;
            }

        } catch (const fs::filesystem_error& e) {
            {
                std::lock_guard<std::mutex> lock(resultMutex_);
                failedRenames_++;
                std::cerr << "ComicPlugin [Thread " << std::this_thread::get_id() << "]: "
                          << "Failed to rename " << filePath.filename().string()
                          << ": " << e.what() << std::endl;
            }
        } catch (const std::exception& e) {
            {
                std::lock_guard<std::mutex> lock(resultMutex_);
                failedRenames_++;
                std::cerr << "ComicPlugin [Thread " << std::this_thread::get_id() << "]: "
                          << "Unexpected error renaming " << filePath.filename().string()
                          << ": " << e.what() << std::endl;
            }
        }

        processedFiles_++;
    }

    // 获取统计信息的方法
    int getProcessedFiles() const { return processedFiles_.load(); }
    int getSuccessfulRenames() const { return successfulRenames_.load(); }
    int getFailedRenames() const { return failedRenames_.load(); }

private:
    std::shared_ptr<MockMediator> mediator_;
    int pluginId_;
    std::string description_;
    std::unique_ptr<zexuan::ThreadPool> threadPool_;
    std::mutex resultMutex_;
    std::atomic<int> processedFiles_;
    std::atomic<int> successfulRenames_;
    std::atomic<int> failedRenames_;
};

// 测试工具类
class ComicPluginTestUtils {
public:
    // 创建测试目录结构
    static void createTestDirectory(const std::string& basePath, const std::vector<std::string>& fileNames) {
        try {
            fs::create_directories(basePath);
            
            for (const auto& fileName : fileNames) {
                std::string filePath = basePath + "/" + fileName;
                std::ofstream file(filePath);
                if (file.is_open()) {
                    file << "Test content for " << fileName << std::endl;
                    file.close();
                }
            }
            
            std::cout << "Created test directory: " << basePath << " with " << fileNames.size() << " files" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "Failed to create test directory: " << e.what() << std::endl;
        }
    }

    // 清理测试目录
    static void cleanupTestDirectory(const std::string& basePath) {
        try {
            if (fs::exists(basePath)) {
                fs::remove_all(basePath);
                std::cout << "Cleaned up test directory: " << basePath << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "Failed to cleanup test directory: " << e.what() << std::endl;
        }
    }

    // 验证文件是否存在
    static bool fileExists(const std::string& filePath) {
        return fs::exists(filePath) && fs::is_regular_file(filePath);
    }

    // 获取目录中的文件列表
    static std::vector<std::string> getFilesInDirectory(const std::string& directoryPath) {
        std::vector<std::string> files;
        try {
            if (fs::exists(directoryPath) && fs::is_directory(directoryPath)) {
                for (const auto& entry : fs::directory_iterator(directoryPath)) {
                    if (entry.is_regular_file()) {
                        files.push_back(entry.path().filename().string());
                    }
                }
            }
        } catch (const std::exception& e) {
            std::cerr << "Error reading directory: " << e.what() << std::endl;
        }
        return files;
    }

    // 验证文件名格式是否正确
    static bool isValidRenamedFileName(const std::string& fileName) {
        // 检查格式：YYYYMMDDHHMMSS_uuid.ext
        if (fileName.length() < 20) return false;
        
        // 检查时间戳部分（前14个字符应该是数字）
        for (int i = 0; i < 14; ++i) {
            if (!std::isdigit(fileName[i])) return false;
        }
        
        // 检查下划线
        if (fileName[14] != '_') return false;
        
        // 检查是否包含UUID格式的字符串
        size_t dotPos = fileName.find_last_of('.');
        if (dotPos == std::string::npos) return false;
        
        std::string uuidPart = fileName.substr(15, dotPos - 15);
        return uuidPart.length() >= 36; // UUID最少36个字符
    }
};
