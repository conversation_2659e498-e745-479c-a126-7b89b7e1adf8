#include "test_comic_plugin.h"
#include <memory>
#include <thread>
#include <chrono>

// ComicPlugin 测试夹具类
class ComicPluginTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建 Mock 对象
        mockMediator = std::make_shared<MockMediator>();
        
        // 创建测试对象
        plugin = std::make_unique<TestableComicPlugin>(mockMediator, 1, "Test Comic Plugin");
        
        // 设置测试目录
        testDirectory = "/tmp/comic_plugin_test";
        
        // 设置默认的 Mock 行为
        SetupDefaultMockBehavior();
    }
    
    void TearDown() override {
        // 清理测试目录
        ComicPluginTestUtils::cleanupTestDirectory(testDirectory);
        
        // 重置对象
        plugin.reset();
        mockMediator.reset();
    }
    
    void SetupDefaultMockBehavior() {
        // 设置 Mediator 的默认行为
        EXPECT_CALL(*mockMediator, initialize())
            .WillRepeatedly(::testing::Return(true));

        EXPECT_CALL(*mockMediator, registerObserver(::testing::_, ::testing::_, ::testing::_))
            .WillRepeatedly(::testing::Return(true));

        EXPECT_CALL(*mockMediator, unregisterObserver(::testing::_, ::testing::_))
            .WillRepeatedly(::testing::Return(true));

        EXPECT_CALL(*mockMediator, sendMessage(::testing::_, ::testing::_))
            .WillRepeatedly(::testing::Return(0));
    }

protected:
    std::shared_ptr<MockMediator> mockMediator;
    std::unique_ptr<TestableComicPlugin> plugin;
    std::string testDirectory;
};

// 测试插件初始化和关闭
TEST_F(ComicPluginTest, InitializeAndShutdown) {
    // 测试初始化
    EXPECT_TRUE(plugin->initialize());
    EXPECT_EQ(plugin->getPluginId(), 1);
    
    // 测试关闭
    plugin->shutdown(); // 应该正常执行，不抛出异常
}

// 测试消息处理 - 文件重命名消息
TEST_F(ComicPluginTest, ProcessFileRenameMessage) {
    // 创建测试文件
    std::vector<std::string> testFiles = {"test1.jpg", "test2.png", "test3.txt"};
    ComicPluginTestUtils::createTestDirectory(testDirectory, testFiles);
    
    // 创建文件重命名消息
    MockMessage message(0x01, 0x81, 0x06, 0x01, 0x01, 0x00, testDirectory);
    
    // 处理消息
    plugin->processMessage(message);
    
    // 验证文件被重命名
    auto filesAfter = ComicPluginTestUtils::getFilesInDirectory(testDirectory);
    EXPECT_EQ(filesAfter.size(), testFiles.size());
    
    // 验证新文件名格式
    for (const auto& fileName : filesAfter) {
        EXPECT_TRUE(ComicPluginTestUtils::isValidRenamedFileName(fileName));
    }
    
    // 验证统计信息
    EXPECT_EQ(plugin->getProcessedFiles(), static_cast<int>(testFiles.size()));
    EXPECT_EQ(plugin->getSuccessfulRenames(), static_cast<int>(testFiles.size()));
    EXPECT_EQ(plugin->getFailedRenames(), 0);
}

// 测试消息处理 - 未知消息类型
TEST_F(ComicPluginTest, ProcessUnknownMessage) {
    // 创建未知类型的消息
    MockMessage message(0x02, 0x81, 0x07, 0x01, 0x02, 0x00, testDirectory);
    
    // 处理消息（应该被忽略）
    plugin->processMessage(message);
    
    // 验证没有文件被处理
    EXPECT_EQ(plugin->getProcessedFiles(), 0);
    EXPECT_EQ(plugin->getSuccessfulRenames(), 0);
    EXPECT_EQ(plugin->getFailedRenames(), 0);
}

// 测试空目录路径处理
TEST_F(ComicPluginTest, HandleEmptyDirectoryPath) {
    // 创建空路径的消息
    MockMessage message(0x01, 0x81, 0x06, 0x01, 0x01, 0x00, "");
    
    // 处理消息
    plugin->processMessage(message);
    
    // 验证没有文件被处理
    EXPECT_EQ(plugin->getProcessedFiles(), 0);
    EXPECT_EQ(plugin->getSuccessfulRenames(), 0);
    EXPECT_EQ(plugin->getFailedRenames(), 0);
}

// 测试无效目录路径处理
TEST_F(ComicPluginTest, HandleInvalidDirectoryPath) {
    std::string invalidPath = "/nonexistent/directory/path";
    
    // 创建无效路径的消息
    MockMessage message(0x01, 0x81, 0x06, 0x01, 0x01, 0x00, invalidPath);
    
    // 处理消息
    plugin->processMessage(message);
    
    // 验证没有文件被处理
    EXPECT_EQ(plugin->getProcessedFiles(), 0);
    EXPECT_EQ(plugin->getSuccessfulRenames(), 0);
    EXPECT_EQ(plugin->getFailedRenames(), 0);
}

// 测试单线程文件夹处理
TEST_F(ComicPluginTest, ProcessFolderSingleThreaded) {
    // 创建测试文件
    std::vector<std::string> testFiles = {"image1.jpg", "image2.png", "document.pdf"};
    ComicPluginTestUtils::createTestDirectory(testDirectory, testFiles);
    
    // 处理文件夹
    bool result = plugin->processFolder(testDirectory);
    
    // 验证处理结果
    EXPECT_TRUE(result);
    EXPECT_EQ(plugin->getProcessedFiles(), static_cast<int>(testFiles.size()));
    EXPECT_EQ(plugin->getSuccessfulRenames(), static_cast<int>(testFiles.size()));
    EXPECT_EQ(plugin->getFailedRenames(), 0);
    
    // 验证文件被重命名
    auto filesAfter = ComicPluginTestUtils::getFilesInDirectory(testDirectory);
    EXPECT_EQ(filesAfter.size(), testFiles.size());
    
    for (const auto& fileName : filesAfter) {
        EXPECT_TRUE(ComicPluginTestUtils::isValidRenamedFileName(fileName));
    }
}

// 测试处理不存在的文件夹
TEST_F(ComicPluginTest, ProcessNonexistentFolder) {
    std::string nonexistentPath = "/tmp/nonexistent_folder";
    
    // 处理不存在的文件夹
    bool result = plugin->processFolder(nonexistentPath);
    
    // 验证处理失败
    EXPECT_FALSE(result);
    EXPECT_EQ(plugin->getProcessedFiles(), 0);
    EXPECT_EQ(plugin->getSuccessfulRenames(), 0);
    EXPECT_EQ(plugin->getFailedRenames(), 0);
}

// 测试处理空文件夹
TEST_F(ComicPluginTest, ProcessEmptyFolder) {
    // 创建空文件夹
    ComicPluginTestUtils::createTestDirectory(testDirectory, {});
    
    // 处理空文件夹
    bool result = plugin->processFolder(testDirectory);
    
    // 验证处理成功但没有文件被处理
    EXPECT_TRUE(result);
    EXPECT_EQ(plugin->getProcessedFiles(), 0);
    EXPECT_EQ(plugin->getSuccessfulRenames(), 0);
    EXPECT_EQ(plugin->getFailedRenames(), 0);
}

// 测试单个文件重命名
TEST_F(ComicPluginTest, RenameSingleFile) {
    // 创建测试文件
    std::vector<std::string> testFiles = {"single_test.jpg"};
    ComicPluginTestUtils::createTestDirectory(testDirectory, testFiles);
    
    fs::path testFilePath = fs::path(testDirectory) / "single_test.jpg";
    
    // 重命名文件
    bool result = plugin->renameFile(testFilePath);
    
    // 验证重命名成功
    EXPECT_TRUE(result);
    
    // 验证原文件不存在
    EXPECT_FALSE(ComicPluginTestUtils::fileExists(testFilePath.string()));
    
    // 验证新文件存在且格式正确
    auto filesAfter = ComicPluginTestUtils::getFilesInDirectory(testDirectory);
    EXPECT_EQ(filesAfter.size(), 1);
    EXPECT_TRUE(ComicPluginTestUtils::isValidRenamedFileName(filesAfter[0]));
}

// 测试文件名生成
TEST_F(ComicPluginTest, GenerateNewFileName) {
    std::string testFilePath = "/tmp/test.jpg";
    
    // 生成新文件名
    std::string newFileName = plugin->generateNewFileName(testFilePath);
    
    // 验证新文件名格式
    EXPECT_TRUE(ComicPluginTestUtils::isValidRenamedFileName(newFileName));
    EXPECT_TRUE(newFileName.size() >= 4 && newFileName.substr(newFileName.size() - 4) == ".jpg");
}

// 测试多线程处理
TEST_F(ComicPluginTest, ProcessDirectoryMultiThreaded) {
    // 创建多个测试文件
    std::vector<std::string> testFiles;
    for (int i = 1; i <= 10; ++i) {
        testFiles.push_back("file" + std::to_string(i) + ".txt");
    }
    ComicPluginTestUtils::createTestDirectory(testDirectory, testFiles);
    
    // 多线程处理目录
    plugin->processDirectoryMultiThreaded(testDirectory);

    // 等待更长时间确保所有线程完成，并且轮询检查
    int maxWaitMs = 5000; // 最多等待5秒
    int waitedMs = 0;
    int stepMs = 50;

    while (waitedMs < maxWaitMs) {
        std::this_thread::sleep_for(std::chrono::milliseconds(stepMs));
        waitedMs += stepMs;

        // 检查是否所有文件都处理完成
        if (plugin->getProcessedFiles() >= static_cast<int>(testFiles.size())) {
            break;
        }
    }

    // 额外等待一点时间确保文件系统操作完成
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 验证处理结果
    EXPECT_EQ(plugin->getProcessedFiles(), static_cast<int>(testFiles.size()));
    EXPECT_EQ(plugin->getSuccessfulRenames(), static_cast<int>(testFiles.size()));
    EXPECT_EQ(plugin->getFailedRenames(), 0);
    
    // 验证文件被重命名
    auto filesAfter = ComicPluginTestUtils::getFilesInDirectory(testDirectory);
    EXPECT_EQ(filesAfter.size(), testFiles.size());
    
    for (const auto& fileName : filesAfter) {
        EXPECT_TRUE(ComicPluginTestUtils::isValidRenamedFileName(fileName));
    }
}

// 测试文件名冲突处理
TEST_F(ComicPluginTest, HandleFileNameConflicts) {
    // 创建测试文件
    std::vector<std::string> testFiles = {"conflict_test.jpg"};
    ComicPluginTestUtils::createTestDirectory(testDirectory, testFiles);
    
    fs::path testFilePath = fs::path(testDirectory) / "conflict_test.jpg";
    
    // 第一次重命名
    bool result1 = plugin->renameFile(testFilePath);
    EXPECT_TRUE(result1);
    
    // 创建另一个同名文件
    std::ofstream newFile(testFilePath);
    newFile << "New content" << std::endl;
    newFile.close();
    
    // 第二次重命名（应该处理冲突）
    bool result2 = plugin->renameFile(testFilePath);
    EXPECT_TRUE(result2);
    
    // 验证有两个不同的文件
    auto filesAfter = ComicPluginTestUtils::getFilesInDirectory(testDirectory);
    EXPECT_EQ(filesAfter.size(), 2);
    
    for (const auto& fileName : filesAfter) {
        EXPECT_TRUE(ComicPluginTestUtils::isValidRenamedFileName(fileName) || 
                   fileName.find("_1.jpg") != std::string::npos);
    }
}

// 主函数
int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
