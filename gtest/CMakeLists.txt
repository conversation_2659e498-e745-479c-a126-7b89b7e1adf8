cmake_minimum_required(VERSION 3.16)

set_property(GLOBAL PROPERTY RULE_LAUNCH_COMPILE ccache)
set_property(GLOBAL PROPERTY RULE_LAUNCH_LINK ccache)

project(gtest_asdu103)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(HOME ${PROJECT_SOURCE_DIR})

function(include_sub_directories_recursively root_dir)
    if (IS_DIRECTORY ${root_dir})
        include_directories(${root_dir})
    else()
        message(WARNING "Directory not found: ${root_dir}")
    endif()

    file(GLOB ALL_SUB RELATIVE ${root_dir} "${root_dir}/*")
    list(SORT ALL_SUB)

    foreach(sub ${ALL_SUB})
        if (IS_DIRECTORY "${root_dir}/${sub}")
            include_sub_directories_recursively("${root_dir}/${sub}")
        endif()
    endforeach()
endfunction()

include_sub_directories_recursively(${CMAKE_SOURCE_DIR}/include)

# 明确指定源文件
set(SRC_FILES
    ${CMAKE_CURRENT_SOURCE_DIR}/src/test_asdu103_gws.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/src/test_utils.cpp
)

set(EXECUTABLE_OUTPUT_PATH ${HOME}/bin)

# 查找依赖包
find_package(GTest REQUIRED)
find_package(GMock REQUIRED)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SRC_FILES})

# 链接库
target_link_libraries(${PROJECT_NAME}
    gtest::gtest
    gtest::gtest_main
    gmock::gmock
    gmock::gmock_main
    pthread
)

# 包含头文件目录
target_include_directories(${PROJECT_NAME} PRIVATE
    ${CMAKE_SOURCE_DIR}/include
)

# 启用测试
enable_testing()

# 添加测试
add_test(NAME TNXEcProAsdu103GWSTest COMMAND ${PROJECT_NAME})

# 设置编译选项
target_compile_options(${PROJECT_NAME} PRIVATE
    -Wall
    -Wextra
    -g
    -O0
)
