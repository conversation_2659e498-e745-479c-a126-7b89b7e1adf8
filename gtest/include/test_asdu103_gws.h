#pragma once

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <string>
#include <vector>
#include <cstring>
#include <cstdint>
#include <algorithm>
#include <cctype>

// 基础类型定义
typedef uint8_t u_int8;
typedef uint16_t u_int16;
typedef uint32_t u_int32;
typedef int32_t int32;

// 参数修饰符
#define IN
#define OUT

// 错误码定义
#define EC_PRO_CVT_FAIL -1
#define EC_PRO_CVT_SUCCESS 0
#define EC_PRO_CVT_NOSUPPORT -2

// 字节序转换宏
#define _REVERSE_BYTE_ORDER_32(x) \
    ((((x) & 0xFF000000) >> 24) | \
     (((x) & 0x00FF0000) >> 8)  | \
     (((x) & 0x0000FF00) << 8)  | \
     (((x) & 0x000000FF) << 24))

// 文件路径分隔符
#define FILE_PATH_OPT_STR "/"

// 协议帧体结构
typedef struct _PRO_FRAME_BODY
{
    u_int8  nType;              // 类型标识
    u_int8  nVsq;               // 可变结构限定词
    u_int8  nCot;               // 传送原因
    u_int16 nSubstationAdd;     // 变电站地址
    u_int16 nAddr;              // 设备地址
    u_int8  nCpu;               // CPU号
    u_int8  nZone;              // 定值区号
    u_int8  nFun;               // 功能类型
    u_int8  nInf;               // 信息序号
    u_int8  nRii;               // 返回信息标识符
    std::vector<u_int8> vVarData; // 可变数据
    
    _PRO_FRAME_BODY()
    {
        nType = 0;
        nVsq = 0;
        nCot = 0;
        nSubstationAdd = 0;
        nAddr = 0;
        nCpu = 0;
        nZone = 0;
        nFun = 0;
        nInf = 0;
        nRii = 0;
    }
} PRO_FRAME_BODY;

typedef std::vector<PRO_FRAME_BODY> PRO_FRAME_BODY_LIST;

// 文件属性信息结构体
typedef struct _FILE_PROPERTY_INF
{
    std::string strFileName;    // 文件名
    u_int32     nFileSize;      // 文件大小
    u_int32     nCreateTime;    // 创建时间
    u_int32     nModifyTime;    // 修改时间
    
    _FILE_PROPERTY_INF()
    {
        nFileSize = 0;
        nCreateTime = 0;
        nModifyTime = 0;
    }
} FILE_PROPERTY_INF;

// 基本配置结构体
typedef struct _BASIC_CFG_TB
{
    std::string str_file_path;      // 文件路径
    std::string str_logroot_path;   // 日志根路径
    
    _BASIC_CFG_TB()
    {
        str_file_path = "/tmp/test_files";
        str_logroot_path = "/tmp/test_logs";
    }
} BASIC_CFG_TB;

// 变电站配置结构体
typedef struct _SUBSTATION_TB
{
    u_int16 n_outaddr103;       // 103规约输出地址
    std::string str_aliasname;  // 变电站别名
    
    _SUBSTATION_TB()
    {
        n_outaddr103 = 1;
        str_aliasname = "TestStation";
    }
} SUBSTATION_TB;

// Mock日志记录类
class MockLogRecord
{
public:
    MOCK_METHOD(void, RecordTraceLog, (const char* msg, const char* className), ());
    MOCK_METHOD(void, RecordErrorLog, (const char* msg, const char* className), ());
    MOCK_METHOD(void, RecordInfoLog, (const char* msg, const char* className), ());
};

// Mock模型查询接口
class MockModelSeek
{
public:
    MOCK_METHOD(bool, GetBasicCfg, (BASIC_CFG_TB& cfg), ());
    MOCK_METHOD(const SUBSTATION_TB*, GetSubStationBasicCfg, (), ());
};

// 模拟系统函数
inline int sy_format_file_path(const char* input, char* output)
{
    if (!input || !output) return -1;
    strcpy(output, input);
    return 0;
}

inline int sy_get_file_property(const char* filename, FILE_PROPERTY_INF* fileInfo)
{
    if (!filename || !fileInfo) return -1;
    
    // 模拟文件存在
    fileInfo->strFileName = filename;
    fileInfo->nFileSize = 1024;
    fileInfo->nCreateTime = 1640995200; // 2022-01-01 00:00:00
    fileInfo->nModifyTime = 1640995200;
    
    return 0;
}

inline int sy_get_file_name(const char* fullPath, char* path, char* name, char* ext)
{
    if (!fullPath || !path || !name || !ext) return -1;
    
    // 简单的文件名解析
    std::string fullPathStr(fullPath);
    size_t lastSlash = fullPathStr.find_last_of("/\\");
    size_t lastDot = fullPathStr.find_last_of(".");
    
    if (lastSlash != std::string::npos) {
        strcpy(path, fullPathStr.substr(0, lastSlash + 1).c_str());
        fullPathStr = fullPathStr.substr(lastSlash + 1);
    } else {
        strcpy(path, "");
    }
    
    if (lastDot != std::string::npos && lastDot > lastSlash) {
        strcpy(name, fullPathStr.substr(0, lastDot - (lastSlash + 1)).c_str());
        strcpy(ext, fullPathStr.substr(lastDot + 1).c_str());
    } else {
        strcpy(name, fullPathStr.c_str());
        strcpy(ext, "");
    }
    
    return 0;
}

// Mock基类，模拟TNXEcProAsdu103的基本功能
class MockTNXEcProAsdu103
{
public:
    MockTNXEcProAsdu103(MockModelSeek* pSeekIns, MockLogRecord* pLogRecord)
        : m_pModelSeek(pSeekIns), m_pLogRecord(pLogRecord) {}

    virtual ~MockTNXEcProAsdu103() = default;

protected:
    MockModelSeek* m_pModelSeek;
    MockLogRecord* m_pLogRecord;
    u_int8 m_nRii;
    u_int8 m_nFun;
    u_int8 m_nInf;

    // 模拟日志记录函数
    void RcdTrcLogWithParentClass(const char* msg, const char* className) {
        if (m_pLogRecord) {
            m_pLogRecord->RecordTraceLog(msg, className);
        }
    }

    void RcdErrLogWithParentClass(const char* msg, const char* className) {
        if (m_pLogRecord) {
            m_pLogRecord->RecordErrorLog(msg, className);
        }
    }

    void _SetLogClassName(const char* className) {
        // 模拟设置日志类名
    }

    // Mock基类方法
    MOCK_METHOD(int, _CvtFileToFrameBody,
                (const char* filePath, int beginPos, FILE_PROPERTY_INF* fileInfo, PRO_FRAME_BODY_LIST& result), ());
    MOCK_METHOD(int, _CvtEmptyFileToFrameBody,
                (int beginPos, const char* fileName, PRO_FRAME_BODY_LIST& result), ());
};

// 测试用的TNXEcProAsdu103GWS实现类
class TestTNXEcProAsdu103GWS : public MockTNXEcProAsdu103
{
public:
    TestTNXEcProAsdu103GWS(MockModelSeek* pSeekIns, MockLogRecord* pLogRecord)
        : MockTNXEcProAsdu103(pSeekIns, pLogRecord)
    {
        _SetLogClassName("TNXEcProAsdu103GWS");
        memset(m_cChr, 0, 1000);
        m_nRobotFileCode = 0;
    }

    // 实现DirectResFromLocal方法（重点测试SettingUp逻辑）
    virtual int DirectResFromLocal(IN PRO_FRAME_BODY* pBody, OUT PRO_FRAME_BODY_LIST& lResult)
    {
        if (!pBody) return EC_PRO_CVT_FAIL;

        char cError[255] = "";
        bool bIsRobot = false;

        if (pBody->vVarData.size() < 105) {
            RcdErrLogWithParentClass("DirectResFromLocal:召唤通用文件报文的长度不够,可能有错误.", "TNXEcProAsdu103GWS");
            return EC_PRO_CVT_FAIL;
        }

        // 获取返回信息标识符
        m_nRii = (u_int8)(pBody->nRii);
        m_nFun = (u_int8)(pBody->nFun);
        m_nInf = (u_int8)(pBody->nInf);

        // 获取召唤的文件名
        char cFileName[255] = "";
        memcpy(cFileName, &(pBody->vVarData[1]), 100);

        std::string strFullFname = cFileName;
        char* psettingfile = _strstr_nocase((char*)strFullFname.c_str(), "SettingUp");

        if (NULL != psettingfile) {
            RcdTrcLogWithParentClass("DirectResFromLocal:发现文件名包含SettingUp", "TNXEcProAsdu103GWS");

            // 获取指定的起始发送位置
            int nBeginSendPos;
            memcpy(&nBeginSendPos, &(pBody->vVarData[101]), 4);
            nBeginSendPos = _REVERSE_BYTE_ORDER_32(nBeginSendPos);

            int npos = strFullFname.find("SettingUp");
            std::string strName = "";
            if (npos != std::string::npos) {
                strName = strFullFname.substr(npos + 9); // 得到SettingUp后面的路径部分
            }

            sprintf(cError, "收到召唤定值文件命令,召唤的文件名为:%s,指定的起始位置:%d", cFileName, nBeginSendPos);
            RcdTrcLogWithParentClass(cError, "TNXEcProAsdu103GWS");

            if (_GeneralFileHandle_SettingUp((char*)strName.c_str(), nBeginSendPos, pBody, lResult, strFullFname) < 0) {
                return EC_PRO_CVT_FAIL;
            }

            RcdTrcLogWithParentClass("发送定值文件数据结束.", "TNXEcProAsdu103GWS");
        } else {
            // 处理其他类型的文件
            char cFilePath[255] = "";
            char _cFileName[255] = "";
            char _cFileExt[255] = "";

            if (sy_get_file_name(cFileName, cFilePath, _cFileName, _cFileExt) != 0) {
                RcdErrLogWithParentClass("DirectResFromLocal:召唤通用文件命令中文件名格式不正确或为空.", "TNXEcProAsdu103GWS");
                return EC_PRO_CVT_FAIL;
            }

            // 其他文件处理逻辑...
        }

        return EC_PRO_CVT_SUCCESS;
    }

    // 实现_GeneralFileHandle_SettingUp方法
    virtual int _GeneralFileHandle_SettingUp(IN const char* cFileName, IN int nBeginSendPos,
                                              IN PRO_FRAME_BODY* pBody, OUT PRO_FRAME_BODY_LIST& lResult,
                                              IN std::string& strFullFname)
    {
        char cError[500] = "";

        // 获取文件存放的路径-定值文件路径
        char cTemp[255] = "";
        char cGeneralFilePathName[500] = ""; // 完整路径加文件名

        BASIC_CFG_TB CfgTb;
        if (!m_pModelSeek->GetBasicCfg(CfgTb)) {
            RcdErrLogWithParentClass("_GeneralFileHandle_SettingUp:获取系统基本配置表信息失败。", "TNXEcProAsdu103GWS");
            return -1;
        }

        if (sy_format_file_path(CfgTb.str_file_path.c_str(), cTemp) < 0) {
            sprintf(cError, "_GeneralFileHandle_SettingUp():格式化定值文件路径[%s]出错.", CfgTb.str_file_path.c_str());
            RcdErrLogWithParentClass(cError, "TNXEcProAsdu103GWS");
            return -1;
        }

        // 拼接路径：基础路径 + "/SettingUp/" + 设备addr地址 + 文件名
        sprintf(cGeneralFilePathName, "%s/SettingUp/%d%s", cTemp, pBody->nAddr, cFileName);

        sprintf(cError, "_GeneralFileHandle_SettingUp:搜索路径[%s],设备addr地址[%d]", cGeneralFilePathName, pBody->nAddr);
        RcdTrcLogWithParentClass(cError, "TNXEcProAsdu103GWS");

        // 判断文件是否存在
        FILE_PROPERTY_INF FileInfo;
        if (sy_get_file_property(cGeneralFilePathName, &FileInfo) != 0) {
            sprintf(cError, "_GeneralFileHandle_SettingUp:文件[%s]不存在或无法访问", cGeneralFilePathName);
            RcdErrLogWithParentClass(cError, "TNXEcProAsdu103GWS");
            return _CvtEmptyFileToFrameBody(nBeginSendPos, cFileName, lResult);
        }

        // 转换数据
        return _CvtFileToFrameBody(cGeneralFilePathName, nBeginSendPos, &FileInfo, lResult);
    }

    // 字符串不区分大小写匹配
    char* _strstr_nocase(const char* str1, const char* str2)
    {
        char* cp = (char*)str1;
        char* s1, * s2;

        if (!*str2) {
            return ((char*)str1);
        }

        while (*cp) {
            s1 = cp;
            s2 = (char*)str2;

            while (*s1 && *s2 && toupper(*s1) == toupper(*s2)) {
                s1++;
                s2++;
            }

            if (!*s2) {
                return cp;
            }

            cp++;
        }
        return NULL;
    }

private:
    char m_cChr[1000];
    int m_nRobotFileCode;
};

// 测试工具函数类
class TestUtils
{
public:
    // 创建测试用的PRO_FRAME_BODY，包含SettingUp文件名
    static PRO_FRAME_BODY CreateSettingUpFrameBody(u_int16 addr = 1,
                                                    const std::string& fileName = "/SettingUp/test.txt",
                                                    int beginPos = 0);

    // 创建测试用的PRO_FRAME_BODY，不包含SettingUp关键字
    static PRO_FRAME_BODY CreateNormalFrameBody(u_int16 addr = 1,
                                                 const std::string& fileName = "normal.txt",
                                                 int beginPos = 0);

    // 验证PRO_FRAME_BODY的基本字段
    static void VerifyFrameBodyBasics(const PRO_FRAME_BODY& body, u_int8 expectedType);

    // 创建测试文件目录结构
    static void CreateTestFileStructure(const std::string& basePath);

    // 清理测试文件
    static void CleanupTestFiles(const std::string& basePath);
};
