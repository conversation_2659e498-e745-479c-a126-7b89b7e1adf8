#include "../include/test_asdu103_gws.h"
#include <memory>

// 测试夹具类
class TNXEcProAsdu103GWSTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        // 创建Mock对象
        mockModelSeek = std::make_unique<MockModelSeek>();
        mockLogRecord = std::make_unique<MockLogRecord>();
        
        // 创建测试对象
        testAsdu103 = std::make_unique<TestTNXEcProAsdu103GWS>(
            mockModelSeek.get(), mockLogRecord.get());
        
        // 设置默认的Mock行为
        SetupDefaultMockBehavior();
        
        // 创建测试文件结构
        TestUtils::CreateTestFileStructure("/tmp/test_files");
    }
    
    void TearDown() override
    {
        // 清理测试文件
        TestUtils::CleanupTestFiles("/tmp/test_files");
        
        // 重置对象
        testAsdu103.reset();
        mockLogRecord.reset();
        mockModelSeek.reset();
    }
    
    void SetupDefaultMockBehavior()
    {
        // 设置GetBasicCfg的默认行为
        ON_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
            .WillByDefault(::testing::Invoke([](BASIC_CFG_TB& cfg) {
                cfg.str_file_path = "/tmp/test_files";
                cfg.str_logroot_path = "/tmp/test_logs";
                return true;
            }));
        
        // 设置GetSubStationBasicCfg的默认行为
        static SUBSTATION_TB stationCfg;
        stationCfg.n_outaddr103 = 1;
        stationCfg.str_aliasname = "TestStation";
        
        ON_CALL(*mockModelSeek, GetSubStationBasicCfg())
            .WillByDefault(::testing::Return(&stationCfg));
        
        // 设置基类方法的默认行为
        ON_CALL(*testAsdu103, _CvtFileToFrameBody(::testing::_, ::testing::_, ::testing::_, ::testing::_))
            .WillByDefault(::testing::Invoke([](const char* filePath, int beginPos, 
                                               FILE_PROPERTY_INF* fileInfo, PRO_FRAME_BODY_LIST& result) {
                // 模拟成功转换文件
                PRO_FRAME_BODY body;
                body.nType = 0x67;
                body.nAddr = 1;
                body.vVarData.resize(50, 0x55); // 填充一些测试数据
                result.push_back(body);
                return 0;
            }));
        
        ON_CALL(*testAsdu103, _CvtEmptyFileToFrameBody(::testing::_, ::testing::_, ::testing::_))
            .WillByDefault(::testing::Invoke([](int beginPos, const char* fileName, PRO_FRAME_BODY_LIST& result) {
                // 模拟空文件转换
                PRO_FRAME_BODY body;
                body.nType = 0x67;
                body.nAddr = 1;
                body.vVarData.resize(10, 0x00); // 空文件数据
                result.push_back(body);
                return 0;
            }));
        
        // 设置日志记录的默认行为（允许任意调用）
        EXPECT_CALL(*mockLogRecord, RecordTraceLog(::testing::_, ::testing::_))
            .Times(::testing::AnyNumber());
        EXPECT_CALL(*mockLogRecord, RecordErrorLog(::testing::_, ::testing::_))
            .Times(::testing::AnyNumber());
    }

protected:
    std::unique_ptr<MockModelSeek> mockModelSeek;
    std::unique_ptr<MockLogRecord> mockLogRecord;
    std::unique_ptr<TestTNXEcProAsdu103GWS> testAsdu103;
};

// 测试DirectResFromLocal到_GeneralFileHandle_SettingUp的完整调用链 - 成功场景
TEST_F(TNXEcProAsdu103GWSTest, DirectResFromLocal_SettingUp_Success)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateSettingUpFrameBody(1, "/SettingUp/test.txt", 0);
    PRO_FRAME_BODY_LIST result;
    
    // 设置期望的Mock调用
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .Times(1)
        .WillOnce(::testing::Invoke([](BASIC_CFG_TB& cfg) {
            cfg.str_file_path = "/tmp/test_files";
            return true;
        }));
    
    EXPECT_CALL(*testAsdu103, _CvtFileToFrameBody(
        ::testing::StrEq("/tmp/test_files/SettingUp/1/test.txt"),
        0,
        ::testing::_,
        ::testing::_))
        .Times(1)
        .WillOnce(::testing::Invoke([](const char* filePath, int beginPos, 
                                       FILE_PROPERTY_INF* fileInfo, PRO_FRAME_BODY_LIST& result) {
            PRO_FRAME_BODY body;
            body.nType = 0x67;
            body.nAddr = 1;
            body.vVarData.resize(100, 0xAA); // 模拟文件数据
            result.push_back(body);
            return 0;
        }));
    
    // 期望的日志调用
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("发现文件名包含SettingUp"), 
        ::testing::StrEq("TNXEcProAsdu103GWS")))
        .Times(1);
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("收到召唤定值文件命令"), 
        ::testing::StrEq("TNXEcProAsdu103GWS")))
        .Times(1);
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("搜索路径"), 
        ::testing::StrEq("TNXEcProAsdu103GWS")))
        .Times(1);
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("发送定值文件数据结束"), 
        ::testing::StrEq("TNXEcProAsdu103GWS")))
        .Times(1);
    
    // 执行测试
    int ret = testAsdu103->DirectResFromLocal(&testBody, result);
    
    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_SUCCESS);
    EXPECT_EQ(result.size(), 1);
    EXPECT_EQ(result[0].nType, 0x67);
    EXPECT_EQ(result[0].nAddr, 1);
    EXPECT_EQ(result[0].vVarData.size(), 100);
}

// 测试DirectResFromLocal - 报文长度不够的情况
TEST_F(TNXEcProAsdu103GWSTest, DirectResFromLocal_InsufficientLength)
{
    // 准备测试数据 - 长度不够
    PRO_FRAME_BODY testBody = TestUtils::CreateSettingUpFrameBody(1, "test.txt", 0);
    testBody.vVarData.resize(50); // 长度不够105
    PRO_FRAME_BODY_LIST result;
    
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(
        ::testing::HasSubstr("召唤通用文件报文的长度不够"), 
        ::testing::StrEq("TNXEcProAsdu103GWS")))
        .Times(1);
    
    // 执行测试
    int ret = testAsdu103->DirectResFromLocal(&testBody, result);
    
    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_FAIL);
    EXPECT_EQ(result.size(), 0);
}

// 测试DirectResFromLocal - 空指针情况
TEST_F(TNXEcProAsdu103GWSTest, DirectResFromLocal_NullPointer)
{
    PRO_FRAME_BODY_LIST result;
    
    // 执行测试
    int ret = testAsdu103->DirectResFromLocal(nullptr, result);
    
    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_FAIL);
    EXPECT_EQ(result.size(), 0);
}

// 测试DirectResFromLocal - 非SettingUp文件名的处理
TEST_F(TNXEcProAsdu103GWSTest, DirectResFromLocal_NonSettingUp)
{
    // 准备测试数据 - 不包含SettingUp关键字
    PRO_FRAME_BODY testBody = TestUtils::CreateNormalFrameBody(1, "normal_file.txt", 0);
    PRO_FRAME_BODY_LIST result;
    
    // 执行测试
    int ret = testAsdu103->DirectResFromLocal(&testBody, result);
    
    // 验证结果 - 应该走其他处理分支，返回成功
    EXPECT_EQ(ret, EC_PRO_CVT_SUCCESS);
}

// 测试_GeneralFileHandle_SettingUp - 获取配置失败
TEST_F(TNXEcProAsdu103GWSTest, GeneralFileHandle_SettingUp_GetConfigFailed)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateSettingUpFrameBody(1, "/SettingUp/test.txt", 0);
    PRO_FRAME_BODY_LIST result;
    std::string strFullFname = "/SettingUp/test.txt";
    
    // 设置Mock行为 - 获取配置失败
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .Times(1)
        .WillOnce(::testing::Return(false));
    
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(
        ::testing::HasSubstr("获取系统基本配置表信息失败"), 
        ::testing::StrEq("TNXEcProAsdu103GWS")))
        .Times(1);
    
    // 执行测试
    int ret = testAsdu103->_GeneralFileHandle_SettingUp("/test.txt", 0, &testBody, result, strFullFname);
    
    // 验证结果
    EXPECT_EQ(ret, -1);
    EXPECT_EQ(result.size(), 0);
}

// 测试_GeneralFileHandle_SettingUp - 文件不存在的情况
TEST_F(TNXEcProAsdu103GWSTest, GeneralFileHandle_SettingUp_FileNotExists)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateSettingUpFrameBody(1, "/SettingUp/nonexistent.txt", 0);
    PRO_FRAME_BODY_LIST result;
    std::string strFullFname = "/SettingUp/nonexistent.txt";

    // 期望调用_CvtEmptyFileToFrameBody来处理不存在的文件
    EXPECT_CALL(*testAsdu103, _CvtEmptyFileToFrameBody(0, ::testing::StrEq("/nonexistent.txt"), ::testing::_))
        .Times(1)
        .WillOnce(::testing::Invoke([](int beginPos, const char* fileName, PRO_FRAME_BODY_LIST& result) {
            PRO_FRAME_BODY body;
            body.nType = 0x67;
            body.nAddr = 1;
            body.vVarData.resize(10, 0x00);
            result.push_back(body);
            return 0;
        }));

    // 期望记录文件不存在的错误日志
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("搜索路径"),
        ::testing::StrEq("TNXEcProAsdu103GWS")))
        .Times(1);

    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::AllOf(::testing::HasSubstr("文件"), ::testing::HasSubstr("不存在")),
        ::testing::StrEq("TNXEcProAsdu103GWS")))
        .Times(1);

    // 执行测试
    int ret = testAsdu103->_GeneralFileHandle_SettingUp("/nonexistent.txt", 0, &testBody, result, strFullFname);

    // 验证结果 - 应该调用空文件处理并返回成功
    EXPECT_EQ(ret, 0);
    EXPECT_EQ(result.size(), 1);
}

// 测试DirectResFromLocal - 不同设备地址的SettingUp文件处理
TEST_F(TNXEcProAsdu103GWSTest, DirectResFromLocal_SettingUp_DifferentDeviceAddr)
{
    // 准备测试数据 - 设备地址为2
    PRO_FRAME_BODY testBody = TestUtils::CreateSettingUpFrameBody(2, "/SettingUp/config.xml", 100);
    PRO_FRAME_BODY_LIST result;

    // 设置期望的Mock调用
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .Times(1)
        .WillOnce(::testing::Invoke([](BASIC_CFG_TB& cfg) {
            cfg.str_file_path = "/tmp/test_files";
            return true;
        }));

    EXPECT_CALL(*testAsdu103, _CvtFileToFrameBody(
        ::testing::StrEq("/tmp/test_files/SettingUp/2/config.xml"),
        100,
        ::testing::_,
        ::testing::_))
        .Times(1)
        .WillOnce(::testing::Return(0));

    // 执行测试
    int ret = testAsdu103->DirectResFromLocal(&testBody, result);

    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_SUCCESS);
}

// 测试_strstr_nocase函数 - 不区分大小写匹配
TEST_F(TNXEcProAsdu103GWSTest, StrStr_NoCase_Function)
{
    // 测试不区分大小写匹配
    char* result1 = testAsdu103->_strstr_nocase("This is a SETTINGUP test", "settingup");
    EXPECT_NE(result1, nullptr);
    EXPECT_STREQ(result1, "SETTINGUP test");

    char* result2 = testAsdu103->_strstr_nocase("No match here", "settingup");
    EXPECT_EQ(result2, nullptr);

    char* result3 = testAsdu103->_strstr_nocase("SettingUp", "SETTINGUP");
    EXPECT_NE(result3, nullptr);
    EXPECT_STREQ(result3, "SettingUp");

    // 测试空字符串
    char* result4 = testAsdu103->_strstr_nocase("test", "");
    EXPECT_NE(result4, nullptr);
    EXPECT_STREQ(result4, "test");
}

// 测试DirectResFromLocal - 完整的调用链验证（集成测试）
TEST_F(TNXEcProAsdu103GWSTest, DirectResFromLocal_SettingUp_FullCallChain)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateSettingUpFrameBody(1, "/SettingUp/integration_test.txt", 50);
    PRO_FRAME_BODY_LIST result;

    // 设置详细的Mock期望，验证完整调用链
    ::testing::InSequence seq;

    // 1. DirectResFromLocal应该首先记录发现SettingUp的日志
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("发现文件名包含SettingUp"),
        ::testing::StrEq("TNXEcProAsdu103GWS")));

    // 2. 然后记录召唤定值文件命令的日志
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("收到召唤定值文件命令"),
        ::testing::StrEq("TNXEcProAsdu103GWS")));

    // 3. _GeneralFileHandle_SettingUp应该获取基本配置
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .WillOnce(::testing::Invoke([](BASIC_CFG_TB& cfg) {
            cfg.str_file_path = "/tmp/test_files";
            return true;
        }));

    // 4. 记录搜索路径的日志
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("搜索路径"),
        ::testing::StrEq("TNXEcProAsdu103GWS")));

    // 5. 调用文件转换方法
    EXPECT_CALL(*testAsdu103, _CvtFileToFrameBody(
        ::testing::StrEq("/tmp/test_files/SettingUp/1/integration_test.txt"),
        50,
        ::testing::_,
        ::testing::_))
        .WillOnce(::testing::Invoke([](const char* filePath, int beginPos,
                                       FILE_PROPERTY_INF* fileInfo, PRO_FRAME_BODY_LIST& result) {
            // 模拟成功转换
            PRO_FRAME_BODY body1, body2;
            body1.nType = 0x67;
            body1.nAddr = 1;
            body1.vVarData.resize(64, 0xCC);

            body2.nType = 0x67;
            body2.nAddr = 1;
            body2.vVarData.resize(32, 0xDD);

            result.push_back(body1);
            result.push_back(body2);
            return 0;
        }));

    // 6. 最后记录发送完成的日志
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("发送定值文件数据结束"),
        ::testing::StrEq("TNXEcProAsdu103GWS")));

    // 执行测试
    int ret = testAsdu103->DirectResFromLocal(&testBody, result);

    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_SUCCESS);
    EXPECT_EQ(result.size(), 2);
    EXPECT_EQ(result[0].vVarData.size(), 64);
    EXPECT_EQ(result[1].vVarData.size(), 32);

    // 验证数据内容
    for (auto& byte : result[0].vVarData) {
        EXPECT_EQ(byte, 0xCC);
    }
    for (auto& byte : result[1].vVarData) {
        EXPECT_EQ(byte, 0xDD);
    }
}

// 测试DirectResFromLocal - _GeneralFileHandle_SettingUp返回失败
TEST_F(TNXEcProAsdu103GWSTest, DirectResFromLocal_SettingUp_HandleFailed)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateSettingUpFrameBody(1, "/SettingUp/fail_test.txt", 0);
    PRO_FRAME_BODY_LIST result;

    // 设置Mock行为 - _CvtFileToFrameBody返回失败
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .WillOnce(::testing::Return(true));

    EXPECT_CALL(*testAsdu103, _CvtFileToFrameBody(::testing::_, ::testing::_, ::testing::_, ::testing::_))
        .WillOnce(::testing::Return(-1)); // 返回失败

    // 执行测试
    int ret = testAsdu103->DirectResFromLocal(&testBody, result);

    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_FAIL);
}

// 主函数
int main(int argc, char** argv)
{
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
