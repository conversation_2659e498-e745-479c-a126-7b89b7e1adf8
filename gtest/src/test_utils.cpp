#include "../include/test_asdu103_gws.h"
#include <filesystem>
#include <fstream>
#include <iostream>

// 创建测试用的PRO_FRAME_BODY，包含SettingUp文件名
PRO_FRAME_BODY TestUtils::CreateSettingUpFrameBody(u_int16 addr, const std::string& fileName, int beginPos)
{
    PRO_FRAME_BODY body;
    body.nType = 0x67; // ASDU103类型
    body.nVsq = 0x81;
    body.nCot = 0x06; // 激活
    body.nSubstationAdd = 1;
    body.nAddr = addr;
    body.nCpu = 0;
    body.nZone = 0;
    body.nFun = 0xFE; // 文件传输
    body.nInf = 0x00;
    body.nRii = 0x01;
    
    // 构造可变数据部分，模拟召唤通用文件的数据格式
    // 需要至少105字节的数据
    body.vVarData.resize(105, 0);
    
    // 设置文件名到可变数据中（从字节1开始，100字节）
    std::string fileNameToSet = fileName;
    if (fileNameToSet.length() > 100) {
        fileNameToSet = fileNameToSet.substr(0, 100);
    }
    memcpy(&body.vVarData[1], fileNameToSet.c_str(), fileNameToSet.length());
    
    // 设置起始发送位置（从字节101开始，4字节，需要字节序转换）
    u_int32 reversedPos = _REVERSE_BYTE_ORDER_32(beginPos);
    memcpy(&body.vVarData[101], &reversedPos, 4);
    
    return body;
}

// 创建测试用的PRO_FRAME_BODY，不包含SettingUp关键字
PRO_FRAME_BODY TestUtils::CreateNormalFrameBody(u_int16 addr, const std::string& fileName, int beginPos)
{
    PRO_FRAME_BODY body;
    body.nType = 0x67; // ASDU103类型
    body.nVsq = 0x81;
    body.nCot = 0x06; // 激活
    body.nSubstationAdd = 1;
    body.nAddr = addr;
    body.nCpu = 0;
    body.nZone = 0;
    body.nFun = 0xFE; // 文件传输
    body.nInf = 0x00;
    body.nRii = 0x01;
    
    // 构造可变数据部分
    body.vVarData.resize(105, 0);
    
    // 设置文件名到可变数据中
    std::string fileNameToSet = fileName;
    if (fileNameToSet.length() > 100) {
        fileNameToSet = fileNameToSet.substr(0, 100);
    }
    memcpy(&body.vVarData[1], fileNameToSet.c_str(), fileNameToSet.length());
    
    // 设置起始发送位置
    u_int32 reversedPos = _REVERSE_BYTE_ORDER_32(beginPos);
    memcpy(&body.vVarData[101], &reversedPos, 4);
    
    return body;
}

// 验证PRO_FRAME_BODY的基本字段
void TestUtils::VerifyFrameBodyBasics(const PRO_FRAME_BODY& body, u_int8 expectedType)
{
    EXPECT_EQ(body.nType, expectedType);
    EXPECT_EQ(body.nVsq, 0x81);
    EXPECT_EQ(body.nCot, 0x06);
    EXPECT_EQ(body.nSubstationAdd, 1);
}

// 创建测试文件目录结构
void TestUtils::CreateTestFileStructure(const std::string& basePath)
{
    try {
        // 创建基础目录
        std::filesystem::create_directories(basePath);
        
        // 创建SettingUp目录结构
        std::string settingUpPath = basePath + "/SettingUp";
        std::filesystem::create_directories(settingUpPath);
        
        // 为不同设备地址创建目录
        for (int addr = 1; addr <= 3; ++addr) {
            std::string devicePath = settingUpPath + "/" + std::to_string(addr);
            std::filesystem::create_directories(devicePath);
            
            // 创建测试文件
            std::string testFile = devicePath + "/test.txt";
            std::ofstream file(testFile);
            if (file.is_open()) {
                file << "Test setting file content for device " << addr << std::endl;
                file << "This is a test file for unit testing." << std::endl;
                file.close();
            }
            
            // 创建另一个测试文件
            std::string configFile = devicePath + "/config.xml";
            std::ofstream configFileStream(configFile);
            if (configFileStream.is_open()) {
                configFileStream << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" << std::endl;
                configFileStream << "<config>" << std::endl;
                configFileStream << "  <device id=\"" << addr << "\">" << std::endl;
                configFileStream << "    <name>Test Device " << addr << "</name>" << std::endl;
                configFileStream << "  </device>" << std::endl;
                configFileStream << "</config>" << std::endl;
                configFileStream.close();
            }
        }
        
        // 创建通用文件目录
        std::string generalPath = basePath + "/通用文件";
        std::filesystem::create_directories(generalPath);
        
        // 创建日志目录
        std::string logPath = basePath + "/logs";
        std::filesystem::create_directories(logPath);
        
        std::cout << "测试文件目录结构创建完成: " << basePath << std::endl;
    }
    catch (const std::exception& e) {
        std::cerr << "创建测试文件目录结构失败: " << e.what() << std::endl;
    }
}

// 创建测试用的PRO_FRAME_BODY，包含SettingUp文件名（ASDU101格式）
PRO_FRAME_BODY TestUtils::CreateAsdu101SettingUpFrameBody(u_int16 addr, const std::string& fileName)
{
    PRO_FRAME_BODY body;
    body.nType = 0x65; // ASDU101类型
    body.nVsq = 0x81;
    body.nCot = 0x06; // 激活
    body.nSubstationAdd = 1;
    body.nAddr = addr;
    body.nCpu = 0;
    body.nZone = 0;
    body.nFun = 0xFE; // 文件传输
    body.nInf = 0x00;
    body.nRii = 0x01;

    // 构造可变数据部分，模拟召唤通用文件列表的数据格式
    // ASDU101需要至少115字节的数据
    body.vVarData.resize(115, 0);

    // 设置时间范围 (假设从字节0-7是开始时间，8-15是结束时间)
    u_int32 startTime = 1640995200; // 2022-01-01 00:00:00
    u_int32 endTime = 1672531200;   // 2023-01-01 00:00:00

    memcpy(&body.vVarData[0], &startTime, 4);
    memcpy(&body.vVarData[8], &endTime, 4);

    // 设置文件名到可变数据中（从字节15开始，100字节）
    std::string fileNameToSet = fileName;
    if (fileNameToSet.length() > 100) {
        fileNameToSet = fileNameToSet.substr(0, 100);
    }
    memcpy(&body.vVarData[15], fileNameToSet.c_str(), fileNameToSet.length());

    return body;
}

// 创建测试用的PRO_FRAME_BODY，不包含SettingUp关键字（ASDU101格式）
PRO_FRAME_BODY TestUtils::CreateAsdu101NormalFrameBody(u_int16 addr, const std::string& fileName)
{
    PRO_FRAME_BODY body;
    body.nType = 0x65; // ASDU101类型
    body.nVsq = 0x81;
    body.nCot = 0x06; // 激活
    body.nSubstationAdd = 1;
    body.nAddr = addr;
    body.nCpu = 0;
    body.nZone = 0;
    body.nFun = 0xFE; // 文件传输
    body.nInf = 0x00;
    body.nRii = 0x01;

    // 构造可变数据部分
    body.vVarData.resize(115, 0);

    // 设置时间范围
    u_int32 startTime = 1640995200;
    u_int32 endTime = 1672531200;

    memcpy(&body.vVarData[0], &startTime, 4);
    memcpy(&body.vVarData[8], &endTime, 4);

    // 设置文件名到可变数据中
    std::string fileNameToSet = fileName;
    if (fileNameToSet.length() > 100) {
        fileNameToSet = fileNameToSet.substr(0, 100);
    }
    memcpy(&body.vVarData[15], fileNameToSet.c_str(), fileNameToSet.length());

    return body;
}

// 清理测试文件
void TestUtils::CleanupTestFiles(const std::string& basePath)
{
    try {
        if (std::filesystem::exists(basePath)) {
            std::filesystem::remove_all(basePath);
            std::cout << "测试文件清理完成: " << basePath << std::endl;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "清理测试文件失败: " << e.what() << std::endl;
    }
}
