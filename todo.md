Atomic.h → <atomic>
copyable.h / noncopyable.h → 删除
Mutex.h → <mutex>
Thread.h → <thread>
Timestamp.h → <chrono>
StringPiece.h → <string_view>
Exception.h → <stdexcept>
Condition.h → <condition_variable>
Singleton.h / ThreadLocal.h
    ThreadLocal → thread_local 关键字
    Singleton → 可以用现代单例模式
Logging.h / LogStream.h / AsyncLogging.h


高优先级（简单替换）：

Atomic.h → <atomic>
copyable.h / noncopyable.h → 删除
Mutex.h → <mutex>
Thread.h → <thread>
中优先级：
5. Timestamp.h → <chrono>
6. StringPiece.h → <string_view>

低优先级（需要更多工作）：
7. 日志相关文件 → spdlog
8. 队列和线程池 → 自定义或第三方库